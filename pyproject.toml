[project]
name = "tr-ass"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
   "aiofiles>=24.1.0",
   "black>=25.1.0",
   "chromadb>=1.0.15",
   "fastapi>=0.116.1",
   "flake8>=7.3.0",
   "httpx>=0.28.1",
   "langchain>=0.3.26",
   "langchain-community>=0.3.27",
   "langchain-core>=0.3.71",
   "langchain-google-genai>=2.1.8",
   "langgraph>=0.5.4",
   "numpy>=2.2.6",
   "openai-whisper>=20250625",
   "pandas>=2.3.1",
   "pinecone>=7.3.0",
   "pyaudio>=0.2.14",
   "pydantic-settings>=2.10.1",
   "pydentic>=0.0.1.dev3",
   "pytest>=8.4.1",
   "pytest-asyncio>=1.1.0",
   "python-dotenv>=1.1.1",
   "python-multipart>=0.0.20",
   "pyttsx3>=2.99",
   "pyyaml>=6.0.2",
   "redis>=6.2.0",
   "sentence-transformers>=5.0.0",
   "speechrecognition>=3.14.3",
   "torch>=2.7.1",
   "torchaudio>=2.7.1",
   "typing-extensions>=4.14.1",
   "uvicorn>=0.35.0",
]
