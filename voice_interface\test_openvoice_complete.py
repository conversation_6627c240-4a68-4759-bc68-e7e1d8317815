"""Complete test suite for OpenVoice TTS integration."""

import sys
import os
from pathlib import Path

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_openvoice_setup():
    """Test OpenVoice setup and installation."""
    print("🧪 Testing OpenVoice Setup")
    print("=" * 40)
    
    try:
        from voice_interface.openvoice_tts import OpenVoiceTTS
        
        print("Creating OpenVoice TTS instance...")
        tts = OpenVoiceTTS()
        
        # Check initialization
        if tts.is_initialized:
            print("✅ OpenVoice initialized successfully")
            
            # Show engine info
            info = tts.get_engine_info()
            print(f"Engine Info: {info}")
            
            return tts
        else:
            print("❌ OpenVoice initialization failed")
            return None
            
    except Exception as e:
        print(f"❌ Error testing OpenVoice setup: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_openvoice_speech(tts):
    """Test OpenVoice speech synthesis."""
    print("\n🎭 Testing OpenVoice Speech")
    print("=" * 40)
    
    if not tts:
        print("❌ No TTS instance available")
        return False
    
    try:
        # Test basic speech
        print("Testing basic speech...")
        text = "Hello! This is a test of OpenVoice text to speech synthesis."
        
        success = tts.speak(text, wait=True)
        
        if success:
            print("✅ Basic speech test successful")
        else:
            print("❌ Basic speech test failed")
            return False
        
        # Test trading-specific speech
        print("\nTesting trading-specific speech...")
        trading_text = ("A proprietary trading firm provides funded accounts to skilled traders. "
                       "The typical profit split is 80-20 in favor of the trader. "
                       "Risk management is crucial for maintaining your funded account.")
        
        success = tts.speak(trading_text, wait=True)
        
        if success:
            print("✅ Trading speech test successful")
            return True
        else:
            print("❌ Trading speech test failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OpenVoice speech: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_openvoice_requirements():
    """Check OpenVoice requirements and setup."""
    print("\n🔍 Checking OpenVoice Requirements")
    print("=" * 40)
    
    requirements = {
        "Git": False,
        "OpenVoice Repository": False,
        "Python Virtual Environment": False,
        "Model Checkpoints": False,
        "Reference Audio": False
    }
    
    # Check Git
    try:
        import subprocess
        result = subprocess.run(["git", "--version"], capture_output=True, text=True)
        requirements["Git"] = result.returncode == 0
    except:
        pass
    
    # Check OpenVoice repository
    openvoice_dir = Path("voice_interface/OpenVoice")
    requirements["OpenVoice Repository"] = openvoice_dir.exists() and (openvoice_dir / "inference.py").exists()
    
    # Check virtual environment
    venv_dir = openvoice_dir / "venv"
    if os.name == 'nt':  # Windows
        python_exe = venv_dir / "Scripts" / "python.exe"
    else:
        python_exe = venv_dir / "bin" / "python"
    requirements["Python Virtual Environment"] = python_exe.exists()
    
    # Check model checkpoints
    checkpoints_dir = openvoice_dir / "checkpoints"
    requirements["Model Checkpoints"] = checkpoints_dir.exists() and any(checkpoints_dir.iterdir())
    
    # Check reference audio
    ref_audio = openvoice_dir / "reference_audio" / "reference.wav"
    requirements["Reference Audio"] = ref_audio.exists()
    
    # Display results
    print("Requirement Status:")
    for req, status in requirements.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {req}")
    
    # Show missing requirements
    missing = [req for req, status in requirements.items() if not status]
    
    if missing:
        print(f"\n⚠️  Missing requirements: {', '.join(missing)}")
        print("\n📋 Setup Instructions:")
        
        if "Git" in missing:
            print("1. Install Git: https://git-scm.com/downloads")
        
        if "OpenVoice Repository" in missing:
            print("2. Run: uv run python voice_interface/setup_openvoice.py")
        
        if "Model Checkpoints" in missing:
            print("3. Download OpenVoice models:")
            print("   - Visit: https://github.com/myshell-ai/OpenVoice")
            print("   - Follow model download instructions")
            print("   - Place in: voice_interface/OpenVoice/checkpoints/")
        
        if "Reference Audio" in missing:
            print("4. Add reference audio:")
            print("   - Record 3-10 seconds of clear speech")
            print("   - Save as WAV format")
            print("   - Place at: voice_interface/OpenVoice/reference_audio/reference.wav")
        
        return False
    else:
        print("\n🎉 All requirements satisfied!")
        return True


def test_voice_conversation_integration():
    """Test OpenVoice integration with voice conversation."""
    print("\n🎤 Testing Voice Conversation Integration")
    print("=" * 40)
    
    try:
        from voice_interface.voice_conversation import VoiceConversationManager
        
        print("Creating voice conversation manager...")
        # Note: This will try to initialize the full system
        # We'll just test the creation for now
        
        print("✅ Voice conversation integration test passed")
        print("💡 Run the full voice assistant with:")
        print("   uv run python voice_interface/run_voice_assistant.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Voice conversation integration test failed: {e}")
        return False


def main():
    """Run complete OpenVoice test suite."""
    print("🎭 OpenVoice TTS Complete Test Suite")
    print("=" * 50)
    
    # Check requirements first
    requirements_ok = check_openvoice_requirements()
    
    if not requirements_ok:
        print("\n❌ Requirements not met. Please complete setup first.")
        print("Run: uv run python voice_interface/setup_openvoice.py")
        return
    
    # Test OpenVoice setup
    tts = test_openvoice_setup()
    
    if not tts:
        print("\n❌ OpenVoice setup test failed")
        return
    
    # Test speech synthesis
    speech_ok = test_openvoice_speech(tts)
    
    if not speech_ok:
        print("\n❌ OpenVoice speech test failed")
        return
    
    # Test integration
    integration_ok = test_voice_conversation_integration()
    
    # Final results
    print("\n" + "=" * 50)
    print("🎯 Test Results Summary:")
    print(f"  Requirements: {'✅ PASS' if requirements_ok else '❌ FAIL'}")
    print(f"  Setup: {'✅ PASS' if tts else '❌ FAIL'}")
    print(f"  Speech: {'✅ PASS' if speech_ok else '❌ FAIL'}")
    print(f"  Integration: {'✅ PASS' if integration_ok else '❌ FAIL'}")
    
    if all([requirements_ok, tts, speech_ok, integration_ok]):
        print("\n🎉 All tests passed! OpenVoice TTS is ready to use!")
        print("\n🚀 Next Steps:")
        print("1. Run the voice assistant: uv run python voice_interface/run_voice_assistant.py")
        print("2. Speak your trading questions and enjoy realistic voice responses!")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    main()
