"""Test script for the new conversation-based API endpoints."""

import requests
import json
import time


def test_conversation_api():
    """Test the new conversation-based API endpoints."""
    base_url = "http://localhost:8000"
    
    print("=" * 60)
    print("Testing New Conversation-Based API")
    print("=" * 60)
    
    # Step 1: Start a new conversation
    print("\n1. Starting a new conversation...")
    try:
        response = requests.post(f"{base_url}/start-conversation", json={})
        
        if response.status_code == 200:
            data = response.json()
            conversation_id = data["conversation_id"]
            print(f"✅ Conversation started successfully!")
            print(f"   Conversation ID: {conversation_id}")
            print(f"   Welcome message: {data['message']}")
        else:
            print(f"❌ Failed to start conversation: {response.status_code}")
            print(f"   Error: {response.text}")
            return
            
    except Exception as e:
        print(f"❌ Error starting conversation: {e}")
        return
    
    # Step 2: Send messages in the conversation
    print(f"\n2. Sending messages in conversation {conversation_id}...")
    
    test_messages = [
        "What are the basic rules for funded trading challenges?",
        "How should I manage my risk when trading with a prop firm?",
        "What happens if I violate the daily loss limit?",
        "Can you give me some tips for passing the evaluation phase?"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n   Message {i}: {message}")
        
        try:
            payload = {"query": message}
            
            start_time = time.time()
            response = requests.post(
                f"{base_url}/chat/{conversation_id}",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Response received ({end_time - start_time:.2f}s)")
                print(f"   Confidence: {data.get('confidence', 0):.3f}")
                print(f"   Sources: {len(data.get('sources', []))}")
                print(f"   Response length: {len(data.get('response', ''))}")
                
                # Show first 150 characters of response
                response_text = data.get('response', '')
                if response_text:
                    preview = response_text[:150] + "..." if len(response_text) > 150 else response_text
                    print(f"   Preview: {preview}")
                
            else:
                print(f"   ❌ Message failed: {response.status_code}")
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Message error: {e}")
        
        # Small delay between messages
        time.sleep(1)
    
    # Step 3: Test conversation memory
    print(f"\n3. Testing conversation memory...")
    
    memory_test_messages = [
        "I'm trading EUR/USD with a $100k account",
        "What position size should I use for this setup?",
        "What if I want to be more conservative with the position size you just suggested?"
    ]
    
    for i, message in enumerate(memory_test_messages, 1):
        print(f"\n   Memory test {i}: {message}")
        
        try:
            payload = {"query": message}
            
            response = requests.post(
                f"{base_url}/chat/{conversation_id}",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Response received")
                print(f"   Confidence: {data.get('confidence', 0):.3f}")
                
                # Show response for memory test
                response_text = data.get('response', '')
                if response_text:
                    preview = response_text[:200] + "..." if len(response_text) > 200 else response_text
                    print(f"   Response: {preview}")
                
            else:
                print(f"   ❌ Memory test failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Memory test error: {e}")
        
        time.sleep(1)
    
    print("\n" + "=" * 60)
    print("Conversation API Test Completed!")
    print("=" * 60)
    print(f"\nConversation ID used: {conversation_id}")
    print("\nAPI Usage Summary:")
    print("1. POST /start-conversation - Creates new conversation")
    print("2. POST /chat/{conversation_id} - Sends messages in conversation")
    print("3. Conversation memory is maintained across messages")


def test_multiple_conversations():
    """Test multiple concurrent conversations."""
    base_url = "http://localhost:8000"
    
    print("\n" + "=" * 60)
    print("Testing Multiple Conversations")
    print("=" * 60)
    
    conversations = []
    
    # Start multiple conversations
    for i in range(3):
        print(f"\nStarting conversation {i+1}...")
        try:
            response = requests.post(f"{base_url}/start-conversation", json={})
            if response.status_code == 200:
                data = response.json()
                conversations.append(data["conversation_id"])
                print(f"✅ Conversation {i+1} started: {data['conversation_id']}")
            else:
                print(f"❌ Failed to start conversation {i+1}")
        except Exception as e:
            print(f"❌ Error starting conversation {i+1}: {e}")
    
    # Send different messages to each conversation
    test_scenarios = [
        "I'm a beginner trader. What should I know about prop firms?",
        "I'm an experienced trader looking for funded accounts. What are the best options?",
        "I failed my first challenge. What went wrong and how can I improve?"
    ]
    
    for i, (conv_id, message) in enumerate(zip(conversations, test_scenarios)):
        print(f"\nSending to conversation {i+1}: {message[:50]}...")
        try:
            payload = {"query": message}
            response = requests.post(f"{base_url}/chat/{conv_id}", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Response received for conversation {i+1}")
                print(f"   Confidence: {data.get('confidence', 0):.3f}")
            else:
                print(f"❌ Failed for conversation {i+1}")
                
        except Exception as e:
            print(f"❌ Error for conversation {i+1}: {e}")
    
    print(f"\n✅ Successfully tested {len(conversations)} concurrent conversations")


if __name__ == "__main__":
    print("Testing the new conversation-based Trading Assistant API")
    print("Make sure the server is running on http://localhost:8000")
    
    # Test basic conversation flow
    test_conversation_api()
    
    # Test multiple conversations
    test_multiple_conversations()
    
    print("\n🎉 All tests completed!")
