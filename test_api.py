"""Simple test script for the Trading Assistant API."""

import requests
import json
import time
from datetime import datetime


def test_api():
    """Test the Trading Assistant API endpoints."""
    base_url = "http://localhost:8000"
    
    print("=" * 60)
    print("Trading Assistant API Test")
    print("=" * 60)
    
    # Test 1: Health check
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            health_data = response.json()
            print(f"   Status: {health_data.get('status')}")
            print(f"   Vector count: {health_data.get('components', {}).get('vector_count', 0)}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    
    # Test 2: Root endpoint
    print("\n2. Testing root endpoint...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ Root endpoint working")
            data = response.json()
            print(f"   Version: {data.get('version')}")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
    
    # Test 3: Stats endpoint
    print("\n3. Testing stats endpoint...")
    try:
        response = requests.get(f"{base_url}/stats")
        if response.status_code == 200:
            print("✅ Stats endpoint working")
            stats = response.json()
            vector_stats = stats.get('vector_store', {})
            print(f"   Total vectors: {vector_stats.get('total_vectors', 0)}")
        else:
            print(f"❌ Stats endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Stats endpoint error: {e}")
    
    # Test 4: Chat endpoint
    print("\n4. Testing chat endpoint...")
    test_queries = [
        "What are the basic rules for funded trading challenges?",
        "How do I manage risk in forex trading?",
        "What should I do if I hit my daily loss limit?",
        "Can you explain prop firm evaluation criteria?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n   Test Query {i}: {query[:50]}...")
        
        try:
            payload = {
                "query": query,
                "session_id": f"test-session-{int(time.time())}"
            }
            
            start_time = time.time()
            response = requests.post(
                f"{base_url}/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Response received ({end_time - start_time:.2f}s)")
                print(f"   Confidence: {data.get('confidence', 0):.3f}")
                print(f"   Sources: {len(data.get('sources', []))}")
                print(f"   Response length: {len(data.get('response', ''))}")
                
                # Show first 100 characters of response
                response_text = data.get('response', '')
                if response_text:
                    preview = response_text[:100] + "..." if len(response_text) > 100 else response_text
                    print(f"   Preview: {preview}")
                
            else:
                print(f"   ❌ Chat request failed: {response.status_code}")
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Chat request error: {e}")
        
        # Small delay between requests
        time.sleep(1)
    
    print("\n" + "=" * 60)
    print("Test completed!")
    print("=" * 60)


def test_conversation_memory():
    """Test conversation memory functionality."""
    base_url = "http://localhost:8000"
    session_id = f"memory-test-{int(time.time())}"
    
    print("\n" + "=" * 60)
    print("Testing Conversation Memory")
    print("=" * 60)
    
    # First message
    print("\n1. First message (establishing context)...")
    payload1 = {
        "query": "I'm trading EUR/USD and my account size is $100k. What position size should I use?",
        "session_id": session_id
    }
    
    try:
        response1 = requests.post(f"{base_url}/chat", json=payload1)
        if response1.status_code == 200:
            print("✅ First message sent")
            data1 = response1.json()
            print(f"   Response length: {len(data1.get('response', ''))}")
        else:
            print(f"❌ First message failed: {response1.status_code}")
    except Exception as e:
        print(f"❌ First message error: {e}")
    
    # Wait a moment
    time.sleep(2)
    
    # Second message (should reference previous context)
    print("\n2. Second message (referencing previous context)...")
    payload2 = {
        "query": "What if I want to be more conservative with my risk?",
        "session_id": session_id
    }
    
    try:
        response2 = requests.post(f"{base_url}/chat", json=payload2)
        if response2.status_code == 200:
            print("✅ Second message sent")
            data2 = response2.json()
            print(f"   Response length: {len(data2.get('response', ''))}")
            
            # Check if response seems contextual
            response_text = data2.get('response', '').lower()
            contextual_indicators = ['eur/usd', '100k', 'account', 'position', 'previous']
            found_context = any(indicator in response_text for indicator in contextual_indicators)
            
            if found_context:
                print("✅ Response appears to reference previous context")
            else:
                print("⚠️  Response may not be using previous context")
                
        else:
            print(f"❌ Second message failed: {response2.status_code}")
    except Exception as e:
        print(f"❌ Second message error: {e}")


if __name__ == "__main__":
    print("Make sure the Trading Assistant is running on localhost:8000")
    print("Start it with: python main.py")
    input("\nPress Enter to continue with the tests...")
    
    test_api()
    test_conversation_memory()
    
    print("\n🎉 All tests completed!")
    print("Check the application logs for more details.")
