"""Test script for enhanced voice output."""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_enhanced_voice():
    """Test enhanced voice output functionality."""
    try:
        print("Testing Enhanced Voice Output...")
        
        from voice_interface.openvoice_output import OpenVoiceOutputHandler
        
        print("Creating enhanced voice handler...")
        handler = OpenVoiceOutputHandler(voice_style="professional")
        
        if not handler.is_initialized:
            print("❌ Enhanced voice handler failed to initialize")
            return False
        
        print("✅ Enhanced voice handler initialized successfully")
        
        # Show engine info
        info = handler.get_engine_info()
        print(f"Engine Info: {info}")
        
        # Test basic speech
        print("\n🎭 Testing basic enhanced speech...")
        success = handler.speak("Hello! This is a test of the enhanced voice system.", wait=True)
        
        if not success:
            print("❌ Basic speech test failed")
            return False
        
        print("✅ Basic speech test successful")
        
        # Test trading-specific speech
        print("\n🎭 Testing trading-specific speech...")
        trading_text = ("A proprietary trading firm, or prop firm, is a company that provides traders with capital to trade financial markets. "
                       "These firms typically offer funded accounts ranging from $10,000 to $200,000 or more. "
                       "The profit split is usually 80/20 or 90/10 in favor of the trader. "
                       "Key requirements include passing an evaluation, maintaining risk management rules, and achieving profit targets.")
        
        success = handler.speak(trading_text, wait=True)
        
        if not success:
            print("❌ Trading speech test failed")
            return False
        
        print("✅ Trading speech test successful")
        
        # Test abbreviations and numbers
        print("\n🎭 Testing abbreviations and numbers...")
        abbrev_text = ("The API returns JSON data with P&L information. "
                      "Your ROI is 15% and you have $5,000 in USD. "
                      "The KYC process requires AML compliance.")
        
        success = handler.speak(abbrev_text, wait=True)
        
        if not success:
            print("❌ Abbreviations test failed")
            return False
        
        print("✅ Abbreviations test successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing enhanced voice: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Enhanced Voice Output Test")
    print("=" * 40)
    
    if test_enhanced_voice():
        print("\n🎉 Enhanced voice output is working!")
        print("The voice should sound more natural with:")
        print("- Better pacing and pauses")
        print("- Proper pronunciation of abbreviations")
        print("- Natural handling of numbers and currency")
        print("- Professional voice selection")
    else:
        print("\n❌ Enhanced voice output test failed")
