"""OpenVoice TTS implementation for realistic voice synthesis."""

import os
import sys
import subprocess
import tempfile
import logging
from typing import Optional, Dict, Any
import threading
import time
import shutil
from pathlib import Path

logger = logging.getLogger(__name__)


class OpenVoiceTTS:
    """OpenVoice TTS handler for realistic voice synthesis."""
    
    def __init__(self, voice_style: str = "professional"):
        """
        Initialize OpenVoice TTS.
        
        Args:
            voice_style: Voice style preference
        """
        self.voice_style = voice_style
        self.is_initialized = False
        self._speaking = False
        
        # OpenVoice paths
        self.openvoice_dir = Path("voice_interface/OpenVoice")
        self.python_exe = sys.executable  # Use current Python executable
        
        # Reference audio for voice cloning
        self.reference_audio = None
        self.use_fallback = False

        # Initialize OpenVoice
        self._setup_openvoice()
    

    
    def _setup_openvoice(self):
        """Set up OpenVoice locally."""
        try:
            print("🎭 Setting up OpenVoice TTS...")

            # Check if OpenVoice is already installed
            if self.openvoice_dir.exists() and (self.openvoice_dir / "inference.py").exists():
                print("✅ OpenVoice repository found")
                self._verify_installation()
                return

            # If directory exists but incomplete, remove and reclone
            if self.openvoice_dir.exists():
                print("🔄 Removing incomplete OpenVoice installation...")
                import shutil
                shutil.rmtree(self.openvoice_dir)

            # Clone OpenVoice repository
            print("📥 Cloning OpenVoice repository...")
            self._clone_openvoice()

            # Dependencies should already be installed by setup script
            print("✅ Using current environment dependencies")

            # Download models
            print("🧠 Setting up models...")
            self._setup_models()

            # Create reference audio
            print("🎤 Setting up reference audio...")
            self._setup_reference_audio()

            self.is_initialized = True
            print("✅ OpenVoice TTS setup completed!")

        except Exception as e:
            logger.error(f"Error setting up OpenVoice: {e}")
            print(f"❌ OpenVoice setup failed: {e}")
            print("🔄 Falling back to enhanced TTS...")
            self._setup_fallback_tts()
    
    def _clone_openvoice(self):
        """Clone the OpenVoice repository."""
        try:
            # Create voice_interface directory if it doesn't exist
            os.makedirs("voice_interface", exist_ok=True)
            
            # Clone the repository
            cmd = [
                "git", "clone", 
                "https://github.com/myshell-ai/OpenVoice.git",
                str(self.openvoice_dir)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd="voice_interface")
            
            if result.returncode != 0:
                raise Exception(f"Git clone failed: {result.stderr}")
            
            print("✅ OpenVoice repository cloned successfully")
            
        except Exception as e:
            raise Exception(f"Failed to clone OpenVoice: {e}")
    

    
    def _setup_models(self):
        """Set up OpenVoice models."""
        try:
            # Check if models are already downloaded
            checkpoints_dir = self.openvoice_dir / "checkpoints"
            
            if not checkpoints_dir.exists():
                print("📥 Downloading OpenVoice models...")
                
                # Create checkpoints directory
                checkpoints_dir.mkdir(exist_ok=True)
                
                # Note: In a real implementation, you would download the actual model files
                # For now, we'll create placeholder structure
                print("⚠️  Model download not implemented - you'll need to manually download models")
                print("   Follow the OpenVoice repository instructions to download checkpoints")
            
            print("✅ Models setup completed")
            
        except Exception as e:
            raise Exception(f"Failed to setup models: {e}")
    
    def _setup_reference_audio(self):
        """Set up reference audio for voice cloning."""
        try:
            # Create a simple reference audio file or use existing one
            ref_audio_dir = self.openvoice_dir / "reference_audio"
            ref_audio_dir.mkdir(exist_ok=True)
            
            self.reference_audio = ref_audio_dir / "reference.wav"
            
            # Note: In a real implementation, you would provide a reference audio file
            # For now, we'll note that it needs to be provided
            if not self.reference_audio.exists():
                print("⚠️  Reference audio not found - you'll need to provide a reference.wav file")
                print(f"   Place your reference audio at: {self.reference_audio}")
            
            print("✅ Reference audio setup completed")
            
        except Exception as e:
            raise Exception(f"Failed to setup reference audio: {e}")
    
    def _verify_installation(self):
        """Verify OpenVoice installation."""
        try:
            # Check if inference script exists
            inference_script = self.openvoice_dir / "inference.py"
            if not inference_script.exists():
                raise Exception("inference.py not found")

            # Check if Python executable exists
            if not os.path.exists(self.python_exe):
                raise Exception("Python executable not found")

            self.is_initialized = True
            print("✅ OpenVoice installation verified")

        except Exception as e:
            print(f"⚠️  OpenVoice verification failed: {e}")
            print("🔄 Falling back to enhanced TTS...")
            self._setup_fallback_tts()

    def _setup_fallback_tts(self):
        """Set up fallback TTS using pyttsx3."""
        try:
            print("🔧 Initializing fallback TTS...")
            import pyttsx3

            self.fallback_engine = pyttsx3.init()

            # Get available voices and select a good one
            voices = self.fallback_engine.getProperty('voices')
            if voices:
                # Try to find a female or enhanced voice
                for voice in voices:
                    voice_name = voice.name.lower()
                    if any(keyword in voice_name for keyword in ['zira', 'female', 'enhanced', 'neural']):
                        self.fallback_engine.setProperty('voice', voice.id)
                        print(f"🎭 Selected fallback voice: {voice.name}")
                        break

            # Set properties for better speech
            self.fallback_engine.setProperty('rate', 160)
            self.fallback_engine.setProperty('volume', 1.0)

            # Test the fallback engine
            print("🧪 Testing fallback TTS...")
            self.fallback_engine.say("Fallback TTS initialized")
            self.fallback_engine.runAndWait()

            self.is_initialized = True
            self.use_fallback = True
            print("✅ Fallback TTS initialized successfully!")

        except Exception as e:
            logger.error(f"Fallback TTS also failed: {e}")
            print(f"❌ Fallback TTS failed: {e}")
            self.is_initialized = False
    
    def speak(self, text: str, wait: bool = True) -> bool:
        """
        Convert text to speech using OpenVoice.
        
        Args:
            text: Text to speak
            wait: Whether to wait for speech to complete
            
        Returns:
            True if speech was successful, False otherwise
        """
        if not self.is_initialized:
            print("❌ OpenVoice not initialized")
            return False
        
        if not text or not text.strip():
            print("⚠️  No text to speak")
            return False
        
        try:
            print(f"🎭 TTS speaking: {text[:100]}{'...' if len(text) > 100 else ''}")

            self._speaking = True

            # Use fallback TTS if OpenVoice is not available
            if hasattr(self, 'use_fallback') and self.use_fallback:
                return self._speak_with_fallback(text, wait)
            else:
                # Use OpenVoice
                if wait:
                    success = self._synthesize_and_play(text)
                    self._speaking = False
                    return success
                else:
                    # Asynchronous speech
                    def speak_async():
                        try:
                            self._synthesize_and_play(text)
                            self._speaking = False
                        except Exception as e:
                            logger.error(f"Error in async OpenVoice speech: {e}")
                            self._speaking = False

                    thread = threading.Thread(target=speak_async)
                    thread.daemon = True
                    thread.start()
                    return True
            
        except Exception as e:
            logger.error(f"Error in OpenVoice speech: {e}")
            print(f"❌ OpenVoice speech error: {e}")
            self._speaking = False
            return False

    def _speak_with_fallback(self, text: str, wait: bool) -> bool:
        """Speak using fallback pyttsx3 TTS."""
        try:
            print("🔧 Using fallback TTS...")

            if wait:
                # Synchronous speech
                self.fallback_engine.say(text)
                self.fallback_engine.runAndWait()
                self._speaking = False
                print("✅ Fallback TTS completed")
                return True
            else:
                # Asynchronous speech
                def speak_async():
                    try:
                        self.fallback_engine.say(text)
                        self.fallback_engine.runAndWait()
                        self._speaking = False
                        print("✅ Fallback TTS completed")
                    except Exception as e:
                        logger.error(f"Error in fallback async speech: {e}")
                        self._speaking = False

                thread = threading.Thread(target=speak_async)
                thread.daemon = True
                thread.start()
                return True

        except Exception as e:
            logger.error(f"Error in fallback TTS: {e}")
            print(f"❌ Fallback TTS error: {e}")
            self._speaking = False
            return False
    
    def _synthesize_and_play(self, text: str) -> bool:
        """Synthesize speech and play it."""
        try:
            # Create temporary output file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                output_path = temp_file.name
            
            # Run OpenVoice inference
            print("🧠 Running OpenVoice inference...")
            
            cmd = [
                str(self.python_exe), "inference.py",
                "--text", text,
                "--ref_audio_path", str(self.reference_audio),
                "--output_path", output_path
            ]
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                cwd=str(self.openvoice_dir),
                timeout=30  # 30 second timeout
            )
            
            if result.returncode != 0:
                print(f"❌ OpenVoice inference failed: {result.stderr}")
                return False
            
            # Play the generated audio
            print("🔊 Playing generated audio...")
            success = self._play_audio(output_path)
            
            # Clean up temporary file
            try:
                os.unlink(output_path)
            except:
                pass
            
            if success:
                print("✅ OpenVoice speech completed")
            
            return success
            
        except subprocess.TimeoutExpired:
            print("⏰ OpenVoice inference timed out")
            return False
        except Exception as e:
            logger.error(f"Error in OpenVoice synthesis: {e}")
            print(f"❌ OpenVoice synthesis error: {e}")
            return False
    
    def _play_audio(self, audio_path: str) -> bool:
        """Play audio file."""
        try:
            import platform
            system = platform.system()
            
            if system == "Windows":
                # Use Windows Media Player
                cmd = ["powershell", "-c", f"(New-Object Media.SoundPlayer '{audio_path}').PlaySync()"]
            elif system == "Darwin":  # macOS
                cmd = ["afplay", audio_path]
            elif system == "Linux":
                cmd = ["aplay", audio_path]
            else:
                print(f"❌ Unsupported system: {system}")
                return False
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"Error playing audio: {e}")
            print(f"❌ Audio playback error: {e}")
            return False
    
    def is_speaking(self) -> bool:
        """Check if currently speaking."""
        return self._speaking
    
    def stop_speaking(self):
        """Stop current speech."""
        try:
            if hasattr(self, 'use_fallback') and self.use_fallback and hasattr(self, 'fallback_engine'):
                self.fallback_engine.stop()

            self._speaking = False
            print("🛑 Speech stopped")
        except Exception as e:
            logger.error(f"Error stopping speech: {e}")
            self._speaking = False
    
    def get_engine_info(self) -> Dict[str, Any]:
        """Get TTS engine information."""
        return {
            "engine": "OpenVoice",
            "voice_style": self.voice_style,
            "is_initialized": self.is_initialized,
            "is_speaking": self._speaking,
            "openvoice_dir": str(self.openvoice_dir),
            "reference_audio": str(self.reference_audio) if self.reference_audio else None
        }


def main():
    """Test OpenVoice TTS."""
    print("OpenVoice TTS Test")
    print("=" * 30)
    
    tts = OpenVoiceTTS()
    
    if tts.is_initialized:
        print("\n🎭 Testing OpenVoice speech...")
        success = tts.speak("Hello! This is a test of OpenVoice text to speech synthesis. It should sound very natural and realistic.")
        
        if success:
            print("✅ OpenVoice test successful!")
        else:
            print("❌ OpenVoice test failed!")
    else:
        print("❌ OpenVoice not initialized")
        print("\nSetup instructions:")
        print("1. Make sure git is installed")
        print("2. Download OpenVoice model checkpoints")
        print("3. Provide a reference audio file")


if __name__ == "__main__":
    main()
