"""Example usage of the new conversation-based API endpoints."""

import requests
import json


class TradingAssistantClient:
    """Simple client for the Trading Assistant conversation API."""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.conversation_id = None
    
    def start_conversation(self):
        """Start a new conversation and get conversation ID."""
        try:
            response = requests.post(f"{self.base_url}/start-conversation", json={})
            
            if response.status_code == 200:
                data = response.json()
                self.conversation_id = data["conversation_id"]
                print(f"🎯 Conversation started: {self.conversation_id}")
                print(f"💬 {data['message']}")
                return True
            else:
                print(f"❌ Failed to start conversation: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error starting conversation: {e}")
            return False
    
    def send_message(self, message):
        """Send a message in the current conversation."""
        if not self.conversation_id:
            print("❌ No active conversation. Please start a conversation first.")
            return None
        
        try:
            payload = {"query": message}
            response = requests.post(
                f"{self.base_url}/chat/{self.conversation_id}",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                return data
            else:
                print(f"❌ Message failed: {response.status_code}")
                print(f"Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error sending message: {e}")
            return None
    
    def chat(self, message):
        """Send a message and display the response."""
        print(f"\n👤 You: {message}")
        
        response = self.send_message(message)
        
        if response:
            print(f"🤖 Assistant: {response['response']}")
            print(f"📊 Confidence: {response['confidence']:.3f}")
            if response['sources']:
                print(f"📚 Sources: {len(response['sources'])} documents")
        
        return response


def example_conversation():
    """Example conversation flow."""
    print("=" * 60)
    print("Trading Assistant - Conversation Example")
    print("=" * 60)
    
    # Create client and start conversation
    client = TradingAssistantClient()
    
    if not client.start_conversation():
        print("Failed to start conversation. Make sure the server is running.")
        return
    
    # Example conversation about prop firm trading
    print("\n" + "=" * 40)
    print("Example: Prop Firm Trading Questions")
    print("=" * 40)
    
    # Question 1: Basic information
    client.chat("What are the main requirements for funded trading challenges?")
    
    # Question 2: Risk management
    client.chat("How should I manage my risk to avoid violating the rules?")
    
    # Question 3: Follow-up question (tests conversation memory)
    client.chat("What specific risk percentage should I use per trade?")
    
    # Question 4: Strategy question
    client.chat("What trading strategies work best for prop firm evaluations?")
    
    print("\n" + "=" * 40)
    print("Example: Personal Trading Scenario")
    print("=" * 40)
    
    # Personal scenario to test memory
    client.chat("I have a $50k funded account and I'm trading forex. My daily loss limit is $2,500.")
    
    # Follow-up questions that should reference the previous context
    client.chat("What position size should I use for EUR/USD trades?")
    
    client.chat("If I want to be more conservative, how should I adjust my position sizing?")
    
    print(f"\n✅ Conversation completed! Conversation ID: {client.conversation_id}")


def interactive_chat():
    """Interactive chat session."""
    print("\n" + "=" * 60)
    print("Interactive Trading Assistant Chat")
    print("=" * 60)
    print("Type 'quit' to exit")
    
    client = TradingAssistantClient()
    
    if not client.start_conversation():
        print("Failed to start conversation. Make sure the server is running.")
        return
    
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye! Thanks for using Trading Assistant!")
                break
            
            if not user_input:
                continue
            
            response = client.send_message(user_input)
            
            if response:
                print(f"\n🤖 Assistant: {response['response']}")
                print(f"📊 Confidence: {response['confidence']:.3f}")
                if response['sources']:
                    print(f"📚 Sources: {len(response['sources'])} documents")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye! Thanks for using Trading Assistant!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    print("Trading Assistant - New Conversation API Example")
    print("Make sure the server is running on http://localhost:8000")
    
    # Run example conversation
    example_conversation()
    
    # Ask if user wants interactive chat
    print("\n" + "=" * 60)
    choice = input("Would you like to try interactive chat? (y/n): ").strip().lower()
    
    if choice in ['y', 'yes']:
        interactive_chat()
    else:
        print("👋 Thanks for trying the Trading Assistant API!")
