2025-07-23 00:48:03,096 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'E:\\tr-ass\\.venv\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-23 00:48:03,096 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'E:\tr-ass\.venv\lib\site-packages\comtypes\gen'
2025-07-23 00:53:53,669 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 00:53:53,669 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 00:54:07,887 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 00:54:07,887 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 00:54:23,182 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'E:\\tr-ass\\.venv\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-23 00:54:23,182 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'E:\tr-ass\.venv\lib\site-packages\comtypes\gen'
2025-07-23 00:57:14,211 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 00:57:14,211 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 00:57:21,386 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 00:57:21,386 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 00:59:57,101 - voice_interface.voice_input - ERROR - Unexpected error in speech recognition: [WinError 10060] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond
2025-07-23 01:03:03,011 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 01:03:03,013 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 01:03:13,535 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 01:03:13,535 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 01:03:29,156 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'E:\\tr-ass\\.venv\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-23 01:03:29,158 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'E:\tr-ass\.venv\lib\site-packages\comtypes\gen'
2025-07-23 01:08:49,928 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 01:08:49,930 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 01:08:58,612 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 01:08:58,614 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 01:09:12,675 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'E:\\tr-ass\\.venv\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-23 01:09:12,677 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'E:\tr-ass\.venv\lib\site-packages\comtypes\gen'
2025-07-23 01:15:57,039 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 01:15:57,041 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 01:16:08,474 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 01:16:08,474 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 01:16:22,816 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'E:\\tr-ass\\.venv\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-23 01:16:22,816 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'E:\tr-ass\.venv\lib\site-packages\comtypes\gen'
2025-07-23 11:57:25,330 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 11:57:25,330 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 12:30:52,548 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 12:30:52,548 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 12:31:11,526 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 12:31:11,528 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 12:31:46,246 - comtypes.client._code_cache - INFO - Could not import comtypes.gen, trying to create it.
2025-07-23 12:31:46,247 - comtypes.client._code_cache - INFO - Created comtypes.gen directory: 'E:\tr-ass\.venv\lib\site-packages\comtypes\gen'
2025-07-23 12:31:46,249 - comtypes.client._code_cache - INFO - Writing __init__.py file: 'E:\tr-ass\.venv\lib\site-packages\comtypes\gen\__init__.py'
2025-07-23 12:31:46,305 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'E:\tr-ass\.venv\lib\site-packages\comtypes\gen'
2025-07-23 12:31:46,468 - comtypes.client._generate - INFO - Could not import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4: No module named 'comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4'
2025-07-23 12:31:46,566 - comtypes.client._generate - INFO - # Generating comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4
2025-07-23 12:31:46,826 - comtypes.client._generate - INFO - # Generating comtypes.gen.SpeechLib
2025-07-23 12:31:46,857 - comtypes.client._generate - INFO - Could not import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0: No module named 'comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0'
2025-07-23 12:31:46,862 - comtypes.client._generate - INFO - # Generating comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0
2025-07-23 12:31:46,954 - comtypes.client._generate - INFO - # Generating comtypes.gen.stdole
2025-07-23 13:33:31,913 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 13:33:31,916 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 13:33:42,915 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 13:33:42,919 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 13:33:56,746 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'E:\\tr-ass\\.venv\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-23 13:33:56,746 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'E:\tr-ass\.venv\lib\site-packages\comtypes\gen'
2025-07-23 13:38:58,936 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 13:38:58,937 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 13:39:08,080 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 13:39:08,080 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 13:39:20,665 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'E:\\tr-ass\\.venv\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-23 13:39:20,666 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'E:\tr-ass\.venv\lib\site-packages\comtypes\gen'
2025-07-23 13:47:46,368 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 13:47:46,368 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 13:47:55,423 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 13:47:55,424 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 22:08:21,453 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 22:08:21,453 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 22:08:30,579 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 22:08:30,580 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 22:08:59,727 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 22:08:59,727 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 22:09:07,208 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 22:09:07,209 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 22:09:19,426 - voice_interface.openvoice_tts - ERROR - Error setting up OpenVoice: Failed to clone OpenVoice: Git clone failed: fatal: destination path 'voice_interface\OpenVoice' already exists and is not an empty directory.

2025-07-23 22:12:18,082 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 22:12:18,082 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 22:12:25,156 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-23 22:12:25,156 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-23 22:12:41,147 - voice_interface.openvoice_tts - ERROR - Error setting up OpenVoice: Failed to clone OpenVoice: Git clone failed: fatal: destination path 'voice_interface\OpenVoice' already exists and is not an empty directory.

