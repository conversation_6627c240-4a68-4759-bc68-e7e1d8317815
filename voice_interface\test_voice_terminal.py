"""Test script for voice terminal interface."""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_voice_terminal():
    """Test the voice terminal interface initialization."""
    try:
        print("Testing voice terminal interface...")
        
        from voice_interface.voice_terminal import VoiceTerminalInterface
        
        interface = VoiceTerminalInterface()
        print("✅ VoiceTerminalInterface created successfully")
        
        # Test dependency checking
        print("Testing dependency check...")
        deps_ok = interface.check_dependencies()
        if deps_ok:
            print("✅ All dependencies available")
        else:
            print("❌ Some dependencies missing")
            return False
        
        print("✅ Voice terminal interface test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing voice terminal: {e}")
        return False

if __name__ == "__main__":
    print("Voice Terminal Test")
    print("=" * 30)
    
    if test_voice_terminal():
        print("\n🎉 Test passed! You can now run:")
        print("uv run python voice_interface/run_voice_assistant.py")
    else:
        print("\n❌ Test failed. Please check the errors above.")
