"""Voice output handler using text-to-speech."""

import pyttsx3
import logging
from typing import Optional, Dict, Any
import threading
import time

logger = logging.getLogger(__name__)


class VoiceOutputHandler:
    """Handles voice output using text-to-speech."""
    
    def __init__(self, rate: int = 200, volume: float = 0.9, voice_index: int = 0):
        """
        Initialize voice output handler.
        
        Args:
            rate: Speech rate (words per minute)
            volume: Volume level (0.0 to 1.0)
            voice_index: Voice index (0 for first available voice)
        """
        self.engine = None
        self.rate = rate
        self.volume = volume
        self.voice_index = voice_index
        self._speaking = False
        self._initialize_engine()
    
    def _initialize_engine(self):
        """Initialize the TTS engine."""
        try:
            self.engine = pyttsx3.init()
            
            # Set properties
            self.engine.setProperty('rate', self.rate)
            self.engine.setProperty('volume', self.volume)
            
            # Set voice
            voices = self.engine.getProperty('voices')
            if voices and len(voices) > self.voice_index:
                self.engine.setProperty('voice', voices[self.voice_index].id)
                print(f"🔊 Using voice: {voices[self.voice_index].name}")
            else:
                print("🔊 Using default voice")
            
            print("✅ Text-to-speech engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing TTS engine: {e}")
            print(f"❌ Error initializing TTS engine: {e}")
            self.engine = None
    
    def speak(self, text: str, wait: bool = True) -> bool:
        """
        Convert text to speech.

        Args:
            text: Text to speak
            wait: Whether to wait for speech to complete

        Returns:
            True if speech was initiated successfully, False otherwise
        """
        if not self.engine:
            print("❌ TTS engine not available")
            return False

        if not text or not text.strip():
            print("⚠️  No text to speak")
            return False

        try:
            print(f"🔊 Speaking: {text[:100]}{'...' if len(text) > 100 else ''}")
            print(f"🔧 TTS Debug - Wait mode: {wait}, Engine available: {self.engine is not None}")

            self._speaking = True

            if wait:
                # Synchronous speech
                print("🔧 Starting synchronous speech...")
                self.engine.say(text)
                print("🔧 Text queued, running engine...")
                self.engine.runAndWait()
                print("🔧 Engine finished running")
                self._speaking = False
                print("✅ Synchronous speech completed")
            else:
                # Asynchronous speech
                print("🔧 Starting asynchronous speech...")
                def speak_async():
                    try:
                        print("🔧 Async thread started")
                        self.engine.say(text)
                        print("🔧 Async text queued, running engine...")
                        self.engine.runAndWait()
                        print("🔧 Async engine finished")
                        self._speaking = False
                        print("✅ Asynchronous speech completed")
                    except Exception as e:
                        print(f"❌ Error in async speech thread: {e}")
                        self._speaking = False

                thread = threading.Thread(target=speak_async)
                thread.daemon = True
                thread.start()
                print("🔧 Async thread started")

            return True

        except Exception as e:
            logger.error(f"Error in text-to-speech: {e}")
            print(f"❌ Error in text-to-speech: {e}")
            self._speaking = False
            return False
    
    def is_speaking(self) -> bool:
        """Check if currently speaking."""
        return self._speaking
    
    def stop_speaking(self):
        """Stop current speech."""
        if self.engine and self._speaking:
            try:
                self.engine.stop()
                self._speaking = False
                print("🛑 Speech stopped")
            except Exception as e:
                logger.error(f"Error stopping speech: {e}")
                print(f"❌ Error stopping speech: {e}")
    
    def set_rate(self, rate: int):
        """Set speech rate."""
        if self.engine:
            try:
                self.rate = rate
                self.engine.setProperty('rate', rate)
                print(f"🎛️  Speech rate set to {rate} WPM")
            except Exception as e:
                logger.error(f"Error setting speech rate: {e}")
                print(f"❌ Error setting speech rate: {e}")
    
    def set_volume(self, volume: float):
        """Set speech volume."""
        if self.engine:
            try:
                self.volume = max(0.0, min(1.0, volume))  # Clamp between 0.0 and 1.0
                self.engine.setProperty('volume', self.volume)
                print(f"🔊 Volume set to {self.volume:.1f}")
            except Exception as e:
                logger.error(f"Error setting volume: {e}")
                print(f"❌ Error setting volume: {e}")
    
    def list_voices(self) -> list:
        """List available voices."""
        if not self.engine:
            return []
        
        try:
            voices = self.engine.getProperty('voices')
            voice_list = []
            
            print("🎭 Available voices:")
            for i, voice in enumerate(voices):
                voice_info = {
                    'index': i,
                    'id': voice.id,
                    'name': voice.name,
                    'languages': getattr(voice, 'languages', []),
                    'gender': getattr(voice, 'gender', 'Unknown')
                }
                voice_list.append(voice_info)
                print(f"  {i}: {voice.name} ({voice.id})")
            
            return voice_list
            
        except Exception as e:
            logger.error(f"Error listing voices: {e}")
            print(f"❌ Error listing voices: {e}")
            return []
    
    def set_voice(self, voice_index: int) -> bool:
        """Set voice by index."""
        if not self.engine:
            return False
        
        try:
            voices = self.engine.getProperty('voices')
            if voices and 0 <= voice_index < len(voices):
                self.voice_index = voice_index
                self.engine.setProperty('voice', voices[voice_index].id)
                print(f"🎭 Voice changed to: {voices[voice_index].name}")
                return True
            else:
                print(f"❌ Invalid voice index: {voice_index}")
                return False
                
        except Exception as e:
            logger.error(f"Error setting voice: {e}")
            print(f"❌ Error setting voice: {e}")
            return False
    
    def get_engine_info(self) -> Dict[str, Any]:
        """Get TTS engine information."""
        if not self.engine:
            return {"status": "not_initialized"}
        
        try:
            voices = self.engine.getProperty('voices')
            current_voice = voices[self.voice_index] if voices and len(voices) > self.voice_index else None
            
            return {
                "status": "initialized",
                "rate": self.rate,
                "volume": self.volume,
                "current_voice": {
                    "index": self.voice_index,
                    "name": current_voice.name if current_voice else "Unknown",
                    "id": current_voice.id if current_voice else "Unknown"
                },
                "available_voices": len(voices) if voices else 0,
                "is_speaking": self._speaking
            }
            
        except Exception as e:
            logger.error(f"Error getting engine info: {e}")
            return {"status": "error", "error": str(e)}


def main():
    """Test the voice output handler."""
    print("Voice Output Handler Test")
    print("=" * 30)
    
    handler = VoiceOutputHandler()
    
    # Test basic speech
    print("\n🔊 Testing basic speech...")
    handler.speak("Hello! This is a test of the text to speech system.")
    
    # List available voices
    print("\n🎭 Listing available voices...")
    voices = handler.list_voices()
    
    # Test different voice if available
    if len(voices) > 1:
        print(f"\n🎭 Testing different voice...")
        handler.set_voice(1)
        handler.speak("This is a different voice.")
        
        # Switch back to original voice
        handler.set_voice(0)
    
    # Test rate and volume changes
    print("\n🎛️  Testing rate and volume changes...")
    handler.set_rate(150)
    handler.set_volume(0.7)
    handler.speak("This is slower and quieter speech.")
    
    # Reset to defaults
    handler.set_rate(200)
    handler.set_volume(0.9)
    
    print("\n✅ Voice output test completed!")


if __name__ == "__main__":
    main()
