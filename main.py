"""Main entry point for the Trading Assistant application."""

import uvicorn
from app.core.app import app
from config import settings


def main():
    """Run the Trading Assistant application."""
    print(f"Starting {settings.app_name} v{settings.app_version}")
    print(f"Server will be available at: http://{settings.host}:{settings.port}")
    print("API Documentation: http://localhost:8000/docs")
    print("Health Check: http://localhost:8000/health")
    print("\nPress Ctrl+C to stop the server")

    uvicorn.run(
        "app.core.app:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )


if __name__ == "__main__":
    main()
