"""Enhanced voice output handler for realistic speech synthesis."""

import logging
from typing import Optional, Dict, Any
import threading
import re

logger = logging.getLogger(__name__)


class OpenVoiceOutputHandler:
    """Handles voice output using OpenVoice for realistic speech synthesis."""
    
    def __init__(self, voice_style: str = "default", device: str = "auto"):
        """
        Initialize OpenVoice output handler.
        
        Args:
            voice_style: Voice style to use ("default", "friendly", "professional")
            device: Device to use ("cpu", "cuda", or "auto")
        """
        self.voice_style = voice_style
        self.device = self._get_device(device)
        self.model = None
        self.is_initialized = False
        self._speaking = False
        
        # Audio settings
        self.sample_rate = 24000  # OpenVoice default sample rate
        
        # Initialize OpenVoice model
        self._initialize_openvoice()
    
    def _get_device(self, device: str) -> str:
        """Determine the best device to use."""
        if device == "auto":
            try:
                import torch
                return "cuda" if torch.cuda.is_available() else "cpu"
            except ImportError:
                return "cpu"
        return device
    
    def _initialize_openvoice(self):
        """Initialize the enhanced voice synthesis."""
        try:
            print("🎭 Initializing enhanced voice synthesis...")
            print("⏳ Setting up reliable TTS...")

            # Try multiple TTS approaches for better reliability
            self.tts_method = None

            # Method 1: Try Windows SAPI directly (most reliable on Windows)
            if self._init_windows_sapi():
                self.tts_method = "sapi"
                print("✅ Using Windows SAPI for TTS")

            # Method 2: Fallback to pyttsx3
            elif self._init_pyttsx3():
                self.tts_method = "pyttsx3"
                print("✅ Using pyttsx3 for TTS")

            else:
                print("❌ No TTS method available")
                self.is_initialized = False
                return

            self.is_initialized = True
            print("✅ Enhanced voice synthesis initialized successfully!")

        except Exception as e:
            logger.error(f"Error initializing voice synthesis: {e}")
            print(f"❌ Error initializing voice synthesis: {e}")
            self.is_initialized = False

    def _init_windows_sapi(self):
        """Initialize Windows SAPI TTS."""
        try:
            import platform
            if platform.system() != "Windows":
                return False

            import win32com.client
            print("🔧 Initializing Windows SAPI...")

            self.sapi_voice = win32com.client.Dispatch("SAPI.SpVoice")

            # Get available voices
            voices = self.sapi_voice.GetVoices()
            print(f"🔧 Found {voices.Count} SAPI voices")

            # Try to select a better voice
            for i in range(voices.Count):
                voice = voices.Item(i)
                voice_name = voice.GetDescription().lower()
                print(f"🔧 SAPI Voice {i}: {voice.GetDescription()}")

                # Prefer female or enhanced voices
                if any(keyword in voice_name for keyword in ['zira', 'female', 'enhanced', 'neural']):
                    self.sapi_voice.Voice = voice
                    print(f"🎭 Selected SAPI voice: {voice.GetDescription()}")
                    break

            # Set speech rate (0-10, default is usually 0)
            self.sapi_voice.Rate = 1  # Slightly faster than default

            # Test SAPI
            print("🔧 Testing SAPI...")
            self.sapi_voice.Speak("Test", 1)  # 1 = synchronous
            print("🔧 SAPI test successful")

            return True

        except ImportError:
            print("🔧 pywin32 not available for SAPI")
            return False
        except Exception as e:
            print(f"🔧 SAPI initialization failed: {e}")
            return False

    def _init_pyttsx3(self):
        """Initialize pyttsx3 TTS."""
        try:
            import pyttsx3
            print("🔧 Initializing pyttsx3...")

            self.engine = pyttsx3.init()

            # Get available voices
            voices = self.engine.getProperty('voices')
            print(f"🔧 Found {len(voices) if voices else 0} pyttsx3 voices")

            # Try to find a better voice
            if voices:
                for voice in voices:
                    voice_name = voice.name.lower()
                    if any(keyword in voice_name for keyword in ['zira', 'neural', 'enhanced']):
                        self.engine.setProperty('voice', voice.id)
                        print(f"🎭 Selected pyttsx3 voice: {voice.name}")
                        break

            # Set properties
            self.engine.setProperty('rate', 160)
            self.engine.setProperty('volume', 1.0)  # Maximum volume

            # Test pyttsx3
            print("🔧 Testing pyttsx3...")
            self.engine.say("Test")
            self.engine.runAndWait()
            print("🔧 pyttsx3 test successful")

            return True

        except Exception as e:
            print(f"🔧 pyttsx3 initialization failed: {e}")
            return False
    

    
    def speak(self, text: str, wait: bool = True) -> bool:
        """
        Convert text to speech using enhanced voice synthesis.

        Args:
            text: Text to speak
            wait: Whether to wait for speech to complete

        Returns:
            True if speech was initiated successfully, False otherwise
        """
        if not self.is_initialized:
            print("❌ Voice output not initialized")
            return False

        if not text or not text.strip():
            print("⚠️  No text to speak")
            return False

        try:
            print(f"🎭 Enhanced TTS speaking: {text[:100]}{'...' if len(text) > 100 else ''}")

            self._speaking = True

            # Pre-process text for more natural speech
            processed_text = self._preprocess_text_for_speech(text)

            # Use the appropriate TTS method
            if self.tts_method == "sapi":
                return self._speak_with_sapi(processed_text, wait)
            elif self.tts_method == "pyttsx3":
                return self._speak_with_pyttsx3(processed_text, wait)
            else:
                print("❌ No TTS method available")
                self._speaking = False
                return False

        except Exception as e:
            logger.error(f"Error in speech synthesis: {e}")
            print(f"❌ Error in speech synthesis: {e}")
            self._speaking = False
            return False

    def _speak_with_sapi(self, text: str, wait: bool) -> bool:
        """Speak using Windows SAPI."""
        try:
            print("🔧 Using Windows SAPI for speech...")

            if wait:
                # Synchronous speech
                self.sapi_voice.Speak(text, 1)  # 1 = synchronous
                self._speaking = False
                print("✅ SAPI speech completed")
                return True
            else:
                # Asynchronous speech
                def speak_async():
                    try:
                        self.sapi_voice.Speak(text, 0)  # 0 = asynchronous
                        self._speaking = False
                        print("✅ SAPI speech completed")
                    except Exception as e:
                        logger.error(f"Error in SAPI async speech: {e}")
                        self._speaking = False

                thread = threading.Thread(target=speak_async)
                thread.daemon = True
                thread.start()
                return True

        except Exception as e:
            logger.error(f"Error in SAPI speech: {e}")
            print(f"❌ SAPI speech error: {e}")
            self._speaking = False
            return False

    def _speak_with_pyttsx3(self, text: str, wait: bool) -> bool:
        """Speak using pyttsx3."""
        try:
            print("🔧 Using pyttsx3 for speech...")

            if wait:
                # Synchronous speech
                print("🔧 Debug: Starting pyttsx3 synchronous speech...")
                self.engine.say(text)
                print("🔧 Debug: Text queued, running engine...")
                self.engine.runAndWait()
                print("🔧 Debug: pyttsx3 engine finished running")
                self._speaking = False
                print("✅ pyttsx3 speech completed")
                return True
            else:
                # Asynchronous speech
                def speak_async():
                    try:
                        self.engine.say(text)
                        self.engine.runAndWait()
                        self._speaking = False
                        print("✅ pyttsx3 speech completed")
                    except Exception as e:
                        logger.error(f"Error in pyttsx3 async speech: {e}")
                        self._speaking = False

                thread = threading.Thread(target=speak_async)
                thread.daemon = True
                thread.start()
                return True

        except Exception as e:
            logger.error(f"Error in pyttsx3 speech: {e}")
            print(f"❌ pyttsx3 speech error: {e}")
            self._speaking = False
            return False

    def _preprocess_text_for_speech(self, text: str) -> str:
        """
        Preprocess text to make it sound more natural when spoken.

        Args:
            text: Original text

        Returns:
            Processed text optimized for speech
        """
        # Add pauses for better pacing
        processed = text.replace('. ', '. ... ')  # Longer pause after sentences
        processed = processed.replace(', ', ', .. ')  # Short pause after commas
        processed = processed.replace(': ', ': .. ')  # Pause after colons
        processed = processed.replace('; ', '; .. ')  # Pause after semicolons

        # Handle abbreviations for better pronunciation
        replacements = {
            'API': 'A P I',
            'URL': 'U R L',
            'HTTP': 'H T T P',
            'JSON': 'J S O N',
            'SQL': 'S Q L',
            'AI': 'A I',
            'ML': 'M L',
            'TTS': 'T T S',
            'FAQ': 'F A Q',
            'CEO': 'C E O',
            'CFO': 'C F O',
            'USD': 'U S D',
            'EUR': 'E U R',
            'GBP': 'G B P',
            'JPY': 'J P Y',
            'P&L': 'profit and loss',
            'ROI': 'R O I',
            'KYC': 'K Y C',
            'AML': 'A M L'
        }

        for abbrev, replacement in replacements.items():
            processed = processed.replace(abbrev, replacement)

        # Handle numbers for better pronunciation
        import re

        # Convert percentages
        processed = re.sub(r'(\d+)%', r'\1 percent', processed)

        # Convert currency amounts
        processed = re.sub(r'\$(\d+)', r'\1 dollars', processed)
        processed = re.sub(r'€(\d+)', r'\1 euros', processed)
        processed = re.sub(r'£(\d+)', r'\1 pounds', processed)

        return processed
    
    def is_speaking(self) -> bool:
        """Check if currently speaking."""
        return self._speaking
    
    def stop_speaking(self):
        """Stop current speech."""
        if self._speaking:
            try:
                # Stop the appropriate TTS engine
                if self.tts_method == "sapi" and hasattr(self, 'sapi_voice'):
                    # SAPI doesn't have a direct stop method, but we can speak empty text
                    self.sapi_voice.Speak("", 3)  # 3 = purge before speak
                elif self.tts_method == "pyttsx3" and hasattr(self, 'engine'):
                    self.engine.stop()

                self._speaking = False
                print("🛑 Speech stopped")
            except Exception as e:
                logger.error(f"Error stopping speech: {e}")
                print(f"❌ Error stopping speech: {e}")
    
    def get_engine_info(self) -> Dict[str, Any]:
        """Get TTS engine information."""
        voice_name = "Unknown"
        if hasattr(self, 'engine'):
            try:
                voices = self.engine.getProperty('voices')
                current_voice = self.engine.getProperty('voice')
                for voice in voices:
                    if voice.id == current_voice:
                        voice_name = voice.name
                        break
            except:
                pass

        return {
            "engine": "Enhanced TTS",
            "voice_name": voice_name,
            "voice_style": self.voice_style,
            "device": self.device,
            "is_initialized": self.is_initialized,
            "is_speaking": self._speaking
        }


def main():
    """Test the Enhanced Voice output handler."""
    print("Enhanced Voice Output Handler Test")
    print("=" * 40)

    handler = OpenVoiceOutputHandler()

    if handler.is_initialized:
        print("\n🎭 Testing enhanced speech...")
        handler.speak("Hello! This is a test of enhanced text to speech synthesis. It should sound more natural and realistic than basic TTS, with better pacing and pronunciation.")

        print("\n✅ Enhanced voice test completed!")

        # Show engine info
        info = handler.get_engine_info()
        print(f"\nEngine Info: {info}")
    else:
        print("\n❌ Enhanced voice initialization failed")


if __name__ == "__main__":
    main()
