"""Enhanced voice output handler for realistic speech synthesis."""

import logging
from typing import Optional, Dict, Any
import threading
import re

logger = logging.getLogger(__name__)


class OpenVoiceOutputHandler:
    """Handles voice output using OpenVoice for realistic speech synthesis."""
    
    def __init__(self, voice_style: str = "default", device: str = "auto"):
        """
        Initialize OpenVoice output handler.
        
        Args:
            voice_style: Voice style to use ("default", "friendly", "professional")
            device: Device to use ("cpu", "cuda", or "auto")
        """
        self.voice_style = voice_style
        self.device = self._get_device(device)
        self.model = None
        self.is_initialized = False
        self._speaking = False
        
        # Audio settings
        self.sample_rate = 24000  # OpenVoice default sample rate
        
        # Initialize OpenVoice model
        self._initialize_openvoice()
    
    def _get_device(self, device: str) -> str:
        """Determine the best device to use."""
        if device == "auto":
            try:
                import torch
                return "cuda" if torch.cuda.is_available() else "cpu"
            except ImportError:
                return "cpu"
        return device
    
    def _initialize_openvoice(self):
        """Initialize the OpenVoice model."""
        try:
            print("🎭 Initializing enhanced voice synthesis...")
            print("⏳ Setting up realistic TTS...")

            # For now, we'll use an enhanced pyttsx3 setup with better voice settings
            # This provides a more realistic feel while being easier to set up
            import pyttsx3

            self.engine = pyttsx3.init()

            # Get available voices
            voices = self.engine.getProperty('voices')

            # Try to find a more natural-sounding voice
            selected_voice = None
            for voice in voices:
                voice_name = voice.name.lower()
                # Prefer female voices or voices with "neural" in the name for more natural sound
                if any(keyword in voice_name for keyword in ['zira', 'neural', 'natural', 'enhanced']):
                    selected_voice = voice
                    break

            if selected_voice:
                self.engine.setProperty('voice', selected_voice.id)
                print(f"🎭 Using enhanced voice: {selected_voice.name}")
            else:
                print("🎭 Using default voice with enhanced settings")

            # Enhanced voice settings for more natural speech
            self.engine.setProperty('rate', 160)  # Slightly slower for clarity
            self.engine.setProperty('volume', 0.95)  # High volume for clarity

            # Try to set additional properties if available
            try:
                # Some TTS engines support pitch control
                self.engine.setProperty('pitch', 50)  # Mid-range pitch
            except:
                pass

            self.is_initialized = True
            print("✅ Enhanced voice synthesis initialized successfully!")

        except ImportError:
            logger.error("pyttsx3 not available. Voice output disabled.")
            print("❌ TTS not available. Install with: pip install pyttsx3")
            self.is_initialized = False

        except Exception as e:
            logger.error(f"Error initializing voice synthesis: {e}")
            print(f"❌ Error initializing voice synthesis: {e}")
            self.is_initialized = False
    

    
    def speak(self, text: str, wait: bool = True) -> bool:
        """
        Convert text to speech using enhanced voice synthesis.

        Args:
            text: Text to speak
            wait: Whether to wait for speech to complete

        Returns:
            True if speech was initiated successfully, False otherwise
        """
        if not self.is_initialized or not hasattr(self, 'engine'):
            print("❌ Voice output not initialized")
            return False

        if not text or not text.strip():
            print("⚠️  No text to speak")
            return False

        try:
            print(f"🎭 Enhanced TTS speaking: {text[:100]}{'...' if len(text) > 100 else ''}")

            self._speaking = True

            # Pre-process text for more natural speech
            processed_text = self._preprocess_text_for_speech(text)

            if wait:
                # Synchronous speech
                self.engine.say(processed_text)
                self.engine.runAndWait()
                self._speaking = False
                print("✅ Speech completed")
                return True
            else:
                # Asynchronous speech
                def speak_async():
                    try:
                        self.engine.say(processed_text)
                        self.engine.runAndWait()
                        self._speaking = False
                        print("✅ Speech completed")
                    except Exception as e:
                        logger.error(f"Error in async speech: {e}")
                        self._speaking = False

                thread = threading.Thread(target=speak_async)
                thread.daemon = True
                thread.start()
                return True

        except Exception as e:
            logger.error(f"Error in speech synthesis: {e}")
            print(f"❌ Error in speech synthesis: {e}")
            self._speaking = False
            return False

    def _preprocess_text_for_speech(self, text: str) -> str:
        """
        Preprocess text to make it sound more natural when spoken.

        Args:
            text: Original text

        Returns:
            Processed text optimized for speech
        """
        # Add pauses for better pacing
        processed = text.replace('. ', '. ... ')  # Longer pause after sentences
        processed = processed.replace(', ', ', .. ')  # Short pause after commas
        processed = processed.replace(': ', ': .. ')  # Pause after colons
        processed = processed.replace('; ', '; .. ')  # Pause after semicolons

        # Handle abbreviations for better pronunciation
        replacements = {
            'API': 'A P I',
            'URL': 'U R L',
            'HTTP': 'H T T P',
            'JSON': 'J S O N',
            'SQL': 'S Q L',
            'AI': 'A I',
            'ML': 'M L',
            'TTS': 'T T S',
            'FAQ': 'F A Q',
            'CEO': 'C E O',
            'CFO': 'C F O',
            'USD': 'U S D',
            'EUR': 'E U R',
            'GBP': 'G B P',
            'JPY': 'J P Y',
            'P&L': 'profit and loss',
            'ROI': 'R O I',
            'KYC': 'K Y C',
            'AML': 'A M L'
        }

        for abbrev, replacement in replacements.items():
            processed = processed.replace(abbrev, replacement)

        # Handle numbers for better pronunciation
        import re

        # Convert percentages
        processed = re.sub(r'(\d+)%', r'\1 percent', processed)

        # Convert currency amounts
        processed = re.sub(r'\$(\d+)', r'\1 dollars', processed)
        processed = re.sub(r'€(\d+)', r'\1 euros', processed)
        processed = re.sub(r'£(\d+)', r'\1 pounds', processed)

        return processed
    
    def is_speaking(self) -> bool:
        """Check if currently speaking."""
        return self._speaking
    
    def stop_speaking(self):
        """Stop current speech."""
        if self._speaking:
            try:
                # Stop the TTS engine
                if hasattr(self, 'engine'):
                    self.engine.stop()

                self._speaking = False
                print("🛑 Speech stopped")
            except Exception as e:
                logger.error(f"Error stopping speech: {e}")
                print(f"❌ Error stopping speech: {e}")
    
    def get_engine_info(self) -> Dict[str, Any]:
        """Get TTS engine information."""
        voice_name = "Unknown"
        if hasattr(self, 'engine'):
            try:
                voices = self.engine.getProperty('voices')
                current_voice = self.engine.getProperty('voice')
                for voice in voices:
                    if voice.id == current_voice:
                        voice_name = voice.name
                        break
            except:
                pass

        return {
            "engine": "Enhanced TTS",
            "voice_name": voice_name,
            "voice_style": self.voice_style,
            "device": self.device,
            "is_initialized": self.is_initialized,
            "is_speaking": self._speaking
        }


def main():
    """Test the Enhanced Voice output handler."""
    print("Enhanced Voice Output Handler Test")
    print("=" * 40)

    handler = OpenVoiceOutputHandler()

    if handler.is_initialized:
        print("\n🎭 Testing enhanced speech...")
        handler.speak("Hello! This is a test of enhanced text to speech synthesis. It should sound more natural and realistic than basic TTS, with better pacing and pronunciation.")

        print("\n✅ Enhanced voice test completed!")

        # Show engine info
        info = handler.get_engine_info()
        print(f"\nEngine Info: {info}")
    else:
        print("\n❌ Enhanced voice initialization failed")


if __name__ == "__main__":
    main()
