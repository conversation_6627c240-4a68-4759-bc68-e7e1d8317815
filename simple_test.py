"""Simple test to verify the conversation API works."""

import requests
import json

def test_api():
    """Test the new conversation API."""
    base_url = "http://localhost:8000"
    
    print("Testing Trading Assistant Conversation API")
    print("=" * 50)
    
    try:
        # Test 1: Start conversation
        print("\n1. Starting new conversation...")
        response = requests.post(f"{base_url}/start-conversation", json={})
        
        if response.status_code == 200:
            data = response.json()
            conversation_id = data["conversation_id"]
            print(f"✅ Success! Conversation ID: {conversation_id}")
            print(f"Welcome: {data['message'][:100]}...")
        else:
            print(f"❌ Failed: {response.status_code}")
            return
        
        # Test 2: Send a message
        print(f"\n2. Sending message to conversation {conversation_id}...")
        message = {"query": "What is a prop firm?"}
        
        response = requests.post(
            f"{base_url}/chat/{conversation_id}",
            json=message
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Response received")
            print(f"Response: {data['response'][:200]}...")
            print(f"Confidence: {data['confidence']:.3f}")
            print(f"Sources: {len(data['sources'])}")
        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")
        
        # Test 3: Follow-up message (test memory)
        print(f"\n3. Testing conversation memory...")
        message2 = {"query": "What are the main requirements?"}
        
        response = requests.post(
            f"{base_url}/chat/{conversation_id}",
            json=message2
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Follow-up response received")
            print(f"Response: {data['response'][:200]}...")
            print(f"Confidence: {data['confidence']:.3f}")
        else:
            print(f"❌ Failed: {response.status_code}")
        
        print(f"\n🎉 Test completed! Conversation ID: {conversation_id}")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure it's running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_api()
