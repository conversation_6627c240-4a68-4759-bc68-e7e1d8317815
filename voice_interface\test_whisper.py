"""Test script for Whisper voice recognition."""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_whisper_dependencies():
    """Test if Whisper dependencies are available."""
    print("Testing Whisper dependencies...")
    
    try:
        import whisper
        print("✅ OpenAI Whisper available")
    except ImportError:
        print("❌ OpenAI Whisper not installed")
        print("Install with: uv add openai-whisper")
        return False
    
    try:
        import torch
        print("✅ PyTorch available")
    except ImportError:
        print("❌ PyTorch not installed")
        print("Install with: uv add torch")
        return False
    
    try:
        import pyaudio
        print("✅ PyAudio available")
    except ImportError:
        print("❌ PyAudio not installed")
        print("Install with: uv add pyaudio")
        return False
    
    return True

def test_whisper_voice_input():
    """Test Whisper voice input functionality."""
    try:
        print("Testing Whisper voice input...")
        
        from voice_interface.whisper_voice_input import WhisperVoiceInputHandler
        
        print("Creating Whisper handler (this may take a moment)...")
        handler = WhisperVoiceInputHandler(model_size="tiny")  # Use tiny model for faster testing
        
        print("Testing microphone and recognition...")
        success = handler.test_microphone()
        
        handler.cleanup()
        return success
        
    except Exception as e:
        print(f"❌ Error testing Whisper: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Whisper Voice Recognition Test")
    print("=" * 40)
    
    if test_whisper_dependencies():
        print("\n✅ All dependencies available")
        
        if test_whisper_voice_input():
            print("\n🎉 Whisper voice recognition is working!")
            print("You can now run the voice assistant with:")
            print("uv run python voice_interface/run_voice_assistant.py")
        else:
            print("\n❌ Whisper voice recognition test failed")
    else:
        print("\n❌ Missing dependencies. Please install them first:")
        print("uv add openai-whisper torch torchaudio pyaudio")
