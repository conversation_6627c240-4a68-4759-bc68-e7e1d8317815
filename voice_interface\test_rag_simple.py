"""Simple test of RAG system without voice interface."""

import sys
import os
import asyncio

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_rag():
    """Test RAG system directly."""
    try:
        print("Testing RAG system...")
        
        from app.rag.langgraph_rag import TradingAssistantRAG
        from app.services.vector_store import PineconeVectorStore
        from app.models.schemas import QueryRequest
        
        print("Initializing vector store...")
        vector_store = PineconeVectorStore()
        
        print("Initializing RAG system...")
        rag_system = TradingAssistantRAG()
        
        print("Creating test query...")
        query_request = QueryRequest(
            query="What is a prop firm?",
            session_id="test-session",
            user_id=None
        )
        
        print("Processing query...")
        response = await rag_system.process_query(query_request)
        
        print(f"✅ Response received: {response.response[:100]}...")
        print(f"Confidence: {response.confidence}")
        print(f"Sources: {len(response.sources)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing RAG: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("RAG System Test")
    print("=" * 30)
    
    result = asyncio.run(test_rag())
    
    if result:
        print("\n🎉 RAG system is working!")
    else:
        print("\n❌ RAG system has issues.")
