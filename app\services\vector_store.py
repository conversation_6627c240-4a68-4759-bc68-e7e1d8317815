"""Vector store service using Pinecone."""

import os
import time
from typing import List, Dict, Any, Optional, Tuple
from pinecone import Pinecone, ServerlessSpec
from sentence_transformers import SentenceTransformer
import numpy as np

from app.models.schemas import DocumentChunk
from config import settings


class PineconeVectorStore:
    """Pinecone vector store for document embeddings."""
    
    def __init__(self):
        self.api_key = settings.pinecone_api_key
        self.environment = settings.pinecone_environment
        self.index_name = settings.pinecone_index_name
        self.embedding_model_name = settings.embedding_model
        self.embedding_dimension = settings.embedding_dimension
        
        # Initialize embedding model
        self.embedding_model = SentenceTransformer(self.embedding_model_name)
        
        # Initialize Pinecone
        self._initialize_pinecone()
    
    def _initialize_pinecone(self):
        """Initialize Pinecone client and index."""
        try:
            # Initialize Pinecone client with new API
            self.pc = Pinecone(api_key=self.api_key)
            
            # Check if index exists, create if not
            if self.index_name not in self.pc.list_indexes().names():
                print(f"Creating Pinecone index: {self.index_name}")
                self.pc.create_index(
                    name=self.index_name,
                    dimension=self.embedding_dimension,
                    metric="cosine",
                    spec=ServerlessSpec(
                        cloud='aws',
                        region='us-east-1'
                    )
                )
                # Wait for index to be ready
                time.sleep(10)
            
            self.index = self.pc.Index(self.index_name)
            print(f"Connected to Pinecone index: {self.index_name}")
            
        except Exception as e:
            print(f"Error initializing Pinecone: {e}")
            raise
    
    def create_embeddings(self, texts: List[str]) -> np.ndarray:
        """Create embeddings for a list of texts."""
        try:
            embeddings = self.embedding_model.encode(texts)
            return embeddings
        except Exception as e:
            print(f"Error creating embeddings: {e}")
            raise
    
    def upsert_documents(self, chunks: List[DocumentChunk]) -> bool:
        """Upsert document chunks to Pinecone."""
        try:
            # Prepare texts for embedding
            texts = [chunk.content for chunk in chunks]
            
            # Create embeddings
            print("Creating embeddings...")
            embeddings = self.create_embeddings(texts)
            
            # Prepare vectors for upsert
            vectors = []
            for i, chunk in enumerate(chunks):
                vector = {
                    "id": chunk.chunk_id,
                    "values": embeddings[i].tolist(),
                    "metadata": {
                        "content": chunk.content,
                        "source": chunk.source,
                        **chunk.metadata
                    }
                }
                vectors.append(vector)
            
            # Upsert in batches
            batch_size = 100
            for i in range(0, len(vectors), batch_size):
                batch = vectors[i:i + batch_size]
                self.index.upsert(vectors=batch)
                print(f"Upserted batch {i//batch_size + 1}/{(len(vectors)-1)//batch_size + 1}")
            
            print(f"Successfully upserted {len(vectors)} vectors to Pinecone")
            return True
            
        except Exception as e:
            print(f"Error upserting documents: {e}")
            return False
    
    def similarity_search(
        self, 
        query: str, 
        top_k: int = 5,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar documents."""
        try:
            # Create query embedding
            query_embedding = self.create_embeddings([query])[0]
            
            # Search in Pinecone
            search_results = self.index.query(
                vector=query_embedding.tolist(),
                top_k=top_k,
                include_metadata=True,
                filter=filter_dict
            )
            
            # Format results
            results = []
            for match in search_results.matches:
                result = {
                    "content": match.metadata.get("content", ""),
                    "score": match.score,
                    "source": match.metadata.get("source", ""),
                    "chunk_id": match.id,
                    "metadata": match.metadata
                }
                results.append(result)
            
            return results
            
        except Exception as e:
            print(f"Error in similarity search: {e}")
            return []
    
    def get_index_stats(self) -> Dict[str, Any]:
        """Get index statistics."""
        try:
            stats = self.index.describe_index_stats()
            return {
                "total_vectors": stats.total_vector_count,
                "dimension": stats.dimension,
                "index_fullness": stats.index_fullness
            }
        except Exception as e:
            print(f"Error getting index stats: {e}")
            return {}
    
    def delete_all_vectors(self) -> bool:
        """Delete all vectors from the index."""
        try:
            self.index.delete(delete_all=True)
            print("Deleted all vectors from index")
            return True
        except Exception as e:
            print(f"Error deleting vectors: {e}")
            return False
