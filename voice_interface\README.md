# Trading Assistant Voice Interface

A voice-enabled interface for the Trading Assistant that allows you to interact using speech input and receive audio responses.

## Features

- 🎤 **Speech-to-Text**: Convert your voice to text using Google's speech recognition
- 🔊 **Text-to-Speech**: Get audio responses using pyttsx3
- 🧠 **RAG Integration**: Uses the existing RAG system and memory management
- 💬 **Natural Conversation**: Maintains conversation context and memory
- 🎛️ **Voice Controls**: Adjust volume, speed, and other settings via voice commands

## Installation

1. Install the required dependencies:
```bash
pip install SpeechRecognition pyttsx3 pyaudio
```

**Note for Windows users**: If pyaudio installation fails, try:
```bash
pip install pipwin
pipwin install pyaudio
```

2. Make sure you have a working microphone and speakers/headphones.

## Usage

### Quick Start

Run the voice assistant:
```bash
python voice_interface/run_voice_assistant.py
```

Or run the terminal interface directly:
```bash
python voice_interface/voice_terminal.py
```

### Voice Commands

#### Trading Questions
- Ask any trading-related question naturally
- Examples:
  - "What is a prop firm?"
  - "How do I manage risk in trading?"
  - "Tell me about funded trading accounts"

#### System Commands
- **"help"** - Show available commands
- **"repeat"** - Repeat the last response
- **"volume up"** / **"volume down"** - Adjust speech volume
- **"speak faster"** / **"speak slower"** - Adjust speech speed
- **"quit"** / **"goodbye"** - Exit the application

## Components

### VoiceInputHandler (`voice_input.py`)
- Handles microphone input and speech recognition
- Uses Google's free speech recognition service
- Configurable timeout and phrase limits
- Automatic ambient noise calibration

### VoiceOutputHandler (`voice_output.py`)
- Handles text-to-speech output
- Uses pyttsx3 for cross-platform TTS
- Configurable voice, rate, and volume
- Support for multiple voices

### VoiceConversationManager (`voice_conversation.py`)
- Integrates voice I/O with the existing RAG system
- Maintains conversation context and memory
- Handles special voice commands
- Manages conversation flow

### VoiceTerminalInterface (`voice_terminal.py`)
- Main terminal application
- Dependency checking and component testing
- Interactive menu system
- Error handling and logging

## Configuration

### Voice Settings
You can customize voice settings by modifying the initialization parameters:

```python
# In voice_conversation.py
self.voice_output = VoiceOutputHandler(
    rate=180,      # Speech rate (words per minute)
    volume=0.9,    # Volume (0.0 to 1.0)
    voice_index=0  # Voice index (0 for first available)
)

self.voice_input = VoiceInputHandler(
    timeout=10,        # Max time to wait for speech start
    phrase_timeout=3   # Max time to wait for phrase completion
)
```

### Logging
Logs are written to `voice_interface.log` in the voice_interface directory.

## Troubleshooting

### Microphone Issues
- Check that your microphone is connected and working
- Ensure microphone permissions are granted to Python
- Try adjusting the timeout settings if recognition is too slow/fast

### Speech Recognition Issues
- Speak clearly and at a normal pace
- Ensure you have an internet connection (Google Speech Recognition requires internet)
- Try reducing background noise

### Text-to-Speech Issues
- Check that your audio output is working
- Try different voice indices if the default voice doesn't work
- Ensure no other applications are using the audio device

### Dependencies
If you encounter import errors:
```bash
# Install missing packages
pip install SpeechRecognition pyttsx3 pyaudio

# For Windows pyaudio issues:
pip install pipwin
pipwin install pyaudio

# For Linux pyaudio issues:
sudo apt-get install portaudio19-dev python3-pyaudio
```

## Integration with Main System

The voice interface reuses all existing components:
- **RAG System**: `app.rag.langgraph_rag.TradingAssistantRAG`
- **Vector Store**: `app.services.vector_store.PineconeVectorStore`
- **Memory Management**: Built-in conversation memory and context
- **Models**: Uses existing `QueryRequest` and `QueryResponse` schemas

No changes to the main application are required - the voice interface is completely self-contained.

## Example Session

```
🎤 Starting Trading Assistant Voice Interface...
✅ All dependencies available

🎯 MAIN MENU
1. Start Voice Conversation
Enter your choice: 1

🤖 Assistant: Hello! I'm your Trading Assistant...
🎤 Listening... (speak now)
👤 User said: What is a prop firm?
🤖 Assistant: A proprietary trading firm, or prop firm, is a company that...
🎤 Listening... (speak now)
👤 User said: What are the requirements?
🤖 Assistant: The main requirements for prop firms typically include...
```

## License

This voice interface is part of the Trading Assistant project and follows the same license terms.
