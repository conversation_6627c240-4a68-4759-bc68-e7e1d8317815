"""RAG system for the trading assistant."""

import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import HumanMessage, SystemMessage

from app.services.vector_store import PineconeVectorS<PERSON>
from app.memory.memory_manager import Memory<PERSON>anager
from app.rag.prompt_templates import (
    TRADING_ASSISTANT_SYSTEM_PROMPT,
    RESPONSE_GENERATION_PROMPT
)
from app.models.schemas import QueryRequest, QueryResponse
from config import settings


class TradingAssistantRAG:
    """Simple RAG system for trading assistance."""

    def __init__(self):
        # Initialize components
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            google_api_key=settings.google_api_key,
            temperature=0.3
        )

        self.vector_store = PineconeVectorStore()
        self.memory_manager = MemoryManager()
    
    def _load_context(self, session_id: str) -> Dict[str, Any]:
        """Load conversation context and memory."""
        try:
            # Get conversation context
            context = self.memory_manager.get_conversation_context(session_id)

            result = {
                "conversation_context": context.messages,
                "short_term_memory": context.short_term_memory,
                "long_term_memory": context.long_term_memory
            }

            print(f"Loaded context: {len(result['conversation_context'])} messages, "
                  f"{len(result['short_term_memory'])} short-term memories, "
                  f"{len(result['long_term_memory'])} long-term memories")

            return result

        except Exception as e:
            print(f"Error loading context: {e}")
            return {
                "conversation_context": [],
                "short_term_memory": [],
                "long_term_memory": []
            }

    def _retrieve_documents(self, query: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Retrieve relevant documents from vector store."""
        try:
            # Enhanced query for better retrieval
            enhanced_query = query

            # Add context from short-term memory if available
            if context.get("short_term_memory"):
                recent_context = " ".join(context["short_term_memory"][-3:])
                enhanced_query = f"{query} Context: {recent_context}"

            # Retrieve documents
            results = self.vector_store.similarity_search(
                query=enhanced_query,
                top_k=5
            )

            print(f"Retrieved {len(results)} relevant documents")
            return results

        except Exception as e:
            print(f"Error retrieving documents: {e}")
            return []

    def _generate_response(self, query: str, retrieved_docs: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate response using LLM."""
        try:
            # Format retrieved documents
            retrieved_text = ""
            for i, doc in enumerate(retrieved_docs, 1):
                retrieved_text += f"\n--- Document {i} (Score: {doc.get('score', 0):.3f}) ---\n"
                retrieved_text += doc.get('content', '')
                retrieved_text += "\n"

            # Format conversation context
            context_text = ""
            for msg in context.get("conversation_context", [])[-5:]:  # Last 5 messages
                role = msg.get('role', 'user')
                content = msg.get('content', '')
                context_text += f"{role}: {content}\n"

            # Format memories
            short_term_text = "\n".join(context.get("short_term_memory", [])[-3:]) if context.get("short_term_memory") else "None"
            long_term_text = "\n".join(context.get("long_term_memory", [])[-5:]) if context.get("long_term_memory") else "None"

            # Generate response
            response_prompt = RESPONSE_GENERATION_PROMPT.format(
                query=query,
                retrieved_docs=retrieved_text,
                conversation_context=context_text,
                short_term_memory=short_term_text,
                long_term_memory=long_term_text
            )

            messages = [
                SystemMessage(content=TRADING_ASSISTANT_SYSTEM_PROMPT),
                HumanMessage(content=response_prompt)
            ]

            response = self.llm.invoke(messages)

            # Calculate confidence score based on retrieval scores
            if retrieved_docs:
                avg_score = sum(doc.get('score', 0) for doc in retrieved_docs) / len(retrieved_docs)
                confidence_score = min(avg_score * 1.2, 1.0)  # Scale and cap at 1.0
            else:
                confidence_score = 0.5  # Default confidence

            print(f"Generated response with confidence: {confidence_score:.3f}")

            return {
                "response": response.content,
                "confidence_score": confidence_score
            }

        except Exception as e:
            print(f"Error generating response: {e}")
            return {
                "response": "I apologize, but I'm having trouble processing your request right now. Please try again.",
                "confidence_score": 0.0
            }

    def _update_memory(self, session_id: str, user_id: Optional[str], query: str, response: str, confidence_score: float, context: Dict[str, Any]):
        """Update conversation memory."""
        try:
            # Update conversation buffer
            new_messages = context.get("conversation_context", []) + [
                {"role": "user", "content": query, "timestamp": datetime.now().isoformat()},
                {"role": "assistant", "content": response, "timestamp": datetime.now().isoformat()}
            ]

            self.memory_manager.store_conversation_buffer(session_id, new_messages)

            # Store important information in short-term memory
            if confidence_score > 0.7:
                memory_content = f"Q: {query} A: {response[:200]}..."
                self.memory_manager.store_short_term_memory(
                    session_id,
                    memory_content,
                    confidence_score
                )

            # Store in long-term memory if highly relevant
            if confidence_score > 0.8:
                self.memory_manager.store_long_term_memory(
                    content=f"High-quality interaction: {query} -> {response[:150]}...",
                    importance_score=confidence_score,
                    session_id=session_id,
                    user_id=user_id
                )

            print("Updated memory systems")

        except Exception as e:
            print(f"Error updating memory: {e}")
    
    async def process_query(self, request: QueryRequest) -> QueryResponse:
        """Process a query through the RAG workflow."""
        try:
            session_id = request.session_id or str(uuid.uuid4())

            print(f"Processing query: {request.query[:100]}...")

            # Step 1: Load context and memory
            context = self._load_context(session_id)

            # Step 2: Retrieve relevant documents
            retrieved_docs = self._retrieve_documents(request.query, context)

            # Step 3: Generate response
            response_data = self._generate_response(request.query, retrieved_docs, context)

            # Step 4: Update memory
            self._update_memory(
                session_id,
                request.user_id,
                request.query,
                response_data["response"],
                response_data["confidence_score"],
                context
            )

            # Create response
            sources = [doc.get("source", "") for doc in retrieved_docs]

            response = QueryResponse(
                response=response_data["response"],
                session_id=session_id,
                sources=sources,
                confidence=response_data["confidence_score"]
            )

            return response

        except Exception as e:
            print(f"Error processing query: {e}")
            return QueryResponse(
                response="I apologize, but I encountered an error processing your request. Please try again.",
                session_id=request.session_id or str(uuid.uuid4()),
                sources=[],
                confidence=0.0
            )
