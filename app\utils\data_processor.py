"""Data processing utilities for WhatsApp chat data."""

import re
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from app.models.schemas import ChatMessage, DocumentChunk
from config import settings


class WhatsAppDataProcessor:
    """Processes WhatsApp chat data for RAG system."""
    
    def __init__(self):
        self.data_file_path = settings.data_file_path
        self.chunk_size = settings.chunk_size
        self.chunk_overlap = settings.chunk_overlap
    
    def parse_whatsapp_chat(self, file_path: Optional[str] = None) -> List[ChatMessage]:
        """Parse WhatsApp chat export file."""
        if file_path is None:
            file_path = self.data_file_path
        
        messages = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # WhatsApp message pattern
            pattern = r'(\d{1,2}/\d{1,2}/\d{2,4}, \d{1,2}:\d{2} [AP]M) - ([^:]+): (.+)'
            
            matches = re.findall(pattern, content, re.MULTILINE)
            
            for match in matches:
                timestamp_str, sender, message_content = match
                
                # Parse timestamp
                try:
                    timestamp = datetime.strptime(timestamp_str, '%m/%d/%y, %I:%M %p')
                except ValueError:
                    try:
                        timestamp = datetime.strptime(timestamp_str, '%d/%m/%y, %I:%M %p')
                    except ValueError:
                        continue
                
                # Clean message content
                message_content = message_content.strip()
                
                # Skip system messages and empty messages
                if not message_content or message_content.startswith('Messages and calls are'):
                    continue
                
                # Determine message type
                message_type = "text"
                if "(file attached)" in message_content:
                    message_type = "file"
                elif message_content.endswith(".opus"):
                    message_type = "voice"
                
                messages.append(ChatMessage(
                    timestamp=timestamp,
                    sender=sender,
                    content=message_content,
                    message_type=message_type
                ))
        
        except Exception as e:
            print(f"Error parsing WhatsApp chat: {e}")
            return []
        
        return messages
    
    def extract_trading_queries(self, messages: List[ChatMessage]) -> List[str]:
        """Extract trading-related queries and responses."""
        trading_keywords = [
            'trading', 'trade', 'forex', 'fx', 'funded', 'account', 'profit', 'loss',
            'pips', 'lot', 'leverage', 'margin', 'drawdown', 'equity', 'balance',
            'order', 'buy', 'sell', 'long', 'short', 'position', 'stop', 'limit',
            'challenge', 'evaluation', 'prop', 'proprietary', 'risk', 'management',
            'strategy', 'analysis', 'chart', 'indicator', 'signal', 'market',
            'currency', 'pair', 'spread', 'commission', 'swap', 'rollover'
        ]
        
        trading_content = []
        
        for message in messages:
            content_lower = message.content.lower()
            
            # Check if message contains trading keywords
            if any(keyword in content_lower for keyword in trading_keywords):
                # Create context with timestamp and sender
                context = f"[{message.timestamp.strftime('%Y-%m-%d %H:%M')}] {message.sender}: {message.content}"
                trading_content.append(context)
        
        return trading_content
    
    def create_document_chunks(self, trading_content: List[str]) -> List[DocumentChunk]:
        """Create document chunks for vector storage."""
        chunks = []
        
        # Combine messages into larger chunks
        current_chunk = ""
        chunk_id = 0
        
        for content in trading_content:
            if len(current_chunk) + len(content) > self.chunk_size:
                if current_chunk:
                    chunks.append(DocumentChunk(
                        content=current_chunk.strip(),
                        metadata={
                            "source": "whatsapp_chat",
                            "chunk_id": chunk_id,
                            "content_type": "trading_query"
                        },
                        chunk_id=f"chunk_{chunk_id}",
                        source="whatsapp_chat"
                    ))
                    chunk_id += 1
                
                current_chunk = content
            else:
                if current_chunk:
                    current_chunk += "\n\n" + content
                else:
                    current_chunk = content
        
        # Add the last chunk
        if current_chunk:
            chunks.append(DocumentChunk(
                content=current_chunk.strip(),
                metadata={
                    "source": "whatsapp_chat",
                    "chunk_id": chunk_id,
                    "content_type": "trading_query"
                },
                chunk_id=f"chunk_{chunk_id}",
                source="whatsapp_chat"
            ))
        
        return chunks
    
    def process_data(self) -> List[DocumentChunk]:
        """Main method to process WhatsApp data into document chunks."""
        print("Processing WhatsApp chat data...")
        
        # Parse messages
        messages = self.parse_whatsapp_chat()
        print(f"Parsed {len(messages)} messages")
        
        # Extract trading content
        trading_content = self.extract_trading_queries(messages)
        print(f"Extracted {len(trading_content)} trading-related messages")
        
        # Create chunks
        chunks = self.create_document_chunks(trading_content)
        print(f"Created {len(chunks)} document chunks")
        
        return chunks
