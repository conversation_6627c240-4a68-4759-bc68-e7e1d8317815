"""Terminal-based voice interface for Trading Assistant."""

import asyncio
import sys
import os
import logging
from typing import Optional

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from voice_interface.voice_conversation import VoiceConversationManager
from voice_interface.whisper_voice_input import WhisperVoiceInputHandler
from voice_interface.voice_output import VoiceOutputHandler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("voice_interface.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class VoiceTerminalInterface:
    """Terminal interface for voice-based trading assistant."""
    
    def __init__(self):
        """Initialize the voice terminal interface."""
        self.conversation_manager = None
        
    def print_banner(self):
        """Print the application banner."""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    TRADING ASSISTANT                         ║
║                   Voice Interface v1.0                      ║
║                                                              ║
║  🎤 Speak your trading questions                            ║
║  🔊 Get voice responses                                      ║
║  🧠 Powered by RAG + Memory                                 ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def print_help(self):
        """Print help information."""
        help_text = """
🎯 VOICE COMMANDS:
   • Ask any trading question naturally
   • "help" - Show available commands
   • "repeat" - Repeat last response
   • "volume up/down" - Adjust speech volume
   • "speak faster/slower" - Adjust speech speed
   • "quit" or "goodbye" - Exit the application

💡 TIPS:
   • Speak clearly and wait for the beep
   • The system will process your speech and respond with voice
   • You can interrupt by pressing Ctrl+C
   • All conversations are logged for your reference

🔧 TROUBLESHOOTING:
   • If microphone doesn't work, check your system settings
   • If speech recognition fails, try speaking more clearly
   • If TTS doesn't work, check your audio output settings
        """
        print(help_text)
    
    def check_dependencies(self) -> bool:
        """Check if all required dependencies are available."""
        print("🔍 Checking dependencies...")
        
        try:
            import speech_recognition as sr
            print("✅ SpeechRecognition available")
        except ImportError:
            print("❌ SpeechRecognition not installed. Run: pip install SpeechRecognition")
            return False
        
        try:
            import pyttsx3
            print("✅ pyttsx3 available")
        except ImportError:
            print("❌ pyttsx3 not installed. Run: pip install pyttsx3")
            return False
        
        try:
            import pyaudio
            print("✅ pyaudio available")
        except ImportError:
            print("❌ pyaudio not installed. Run: pip install pyaudio")
            print("   Note: On Windows, you might need: pip install pipwin && pipwin install pyaudio")
            return False

        try:
            import whisper
            print("✅ OpenAI Whisper available")
        except ImportError:
            print("❌ OpenAI Whisper not installed. Run: pip install openai-whisper")
            return False

        try:
            import torch
            print("✅ PyTorch available")
        except ImportError:
            print("❌ PyTorch not installed. Run: pip install torch")
            return False

        return True
    
    def test_components(self) -> bool:
        """Test voice input and output components."""
        print("\n🧪 Testing voice components...")
        
        # Test voice output
        print("🔊 Testing text-to-speech...")
        try:
            voice_output = VoiceOutputHandler()
            voice_output.speak("Voice output test successful", wait=True)
            print("✅ Text-to-speech working")
        except Exception as e:
            print(f"❌ Text-to-speech error: {e}")
            return False
        
        # Test voice input with Whisper
        print("🎤 Testing Whisper speech recognition...")
        try:
            voice_input = WhisperVoiceInputHandler(model_size="tiny")  # Use tiny model for testing
            print("Please say 'test' to verify microphone and Whisper...")
            result = voice_input.listen_for_speech()
            if result:
                print(f"✅ Whisper speech recognition working: '{result}'")
            else:
                print("⚠️  No speech detected, but microphone seems to be working")
            voice_input.cleanup()
        except Exception as e:
            print(f"❌ Whisper speech recognition error: {e}")
            return False
        
        return True
    
    async def run_interactive_mode(self):
        """Run the interactive voice conversation."""
        try:
            print("\n🚀 Starting interactive voice mode...")
            
            # Initialize conversation manager
            self.conversation_manager = VoiceConversationManager()
            
            # Start the conversation
            await self.conversation_manager.start_conversation()
            
        except KeyboardInterrupt:
            print("\n👋 Interrupted by user")
        except Exception as e:
            logger.error(f"Error in interactive mode: {e}")
            print(f"❌ Error: {e}")
        finally:
            if self.conversation_manager:
                self.conversation_manager.stop_conversation()
    
    async def run_menu(self):
        """Run the main menu."""
        while True:
            print("\n" + "="*50)
            print("🎯 MAIN MENU")
            print("="*50)
            print("1. Start Voice Conversation")
            print("2. Test Voice Components")
            print("3. Show Help")
            print("4. Exit")
            print("="*50)

            try:
                choice = input("Enter your choice (1-4): ").strip()

                if choice == '1':
                    print("\n🎤 Starting voice conversation...")
                    await self.run_interactive_mode()

                elif choice == '2':
                    if not self.test_components():
                        print("❌ Component test failed. Please fix the issues before starting.")

                elif choice == '3':
                    self.print_help()

                elif choice == '4':
                    print("👋 Goodbye!")
                    break

                else:
                    print("❌ Invalid choice. Please enter 1-4.")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    async def run(self):
        """Run the voice terminal interface."""
        self.print_banner()
        
        # Check dependencies
        if not self.check_dependencies():
            print("\n❌ Missing dependencies. Please install them and try again.")
            return
        
        print("✅ All dependencies available")
        
        # Show menu
        await self.run_menu()


def main():
    """Main entry point."""
    try:
        interface = VoiceTerminalInterface()
        asyncio.run(interface.run())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"❌ Fatal error: {e}")


if __name__ == "__main__":
    main()
