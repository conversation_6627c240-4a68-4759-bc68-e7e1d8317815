"""FastAPI application for the Trading Assistant."""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager
import logging
from datetime import datetime

from app.models.schemas import (
    QueryRequest, QueryResponse,
    StartConversationRequest, StartConversationResponse,
    ChatRequest, ChatResponse
)
from app.rag.langgraph_rag import TradingAssistantRAG
from app.utils.data_processor import WhatsAppDataProcessor
from app.services.vector_store import PineconeVectorStore
from config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("logs/app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Global variables for components
rag_system: TradingAssistantRAG = None
vector_store: PineconeVectorStore = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global rag_system, vector_store
    
    logger.info("Starting Trading Assistant application...")
    
    try:
        # Initialize components
        logger.info("Initializing vector store...")
        vector_store = PineconeVectorStore()
        
        logger.info("Initializing RAG system...")
        rag_system = TradingAssistantRAG()
        
        # Check if we need to process and index data
        index_stats = vector_store.get_index_stats()
        if index_stats.get("total_vectors", 0) == 0:
            logger.info("No vectors found in index. Processing and indexing data...")
            await process_and_index_data()
        else:
            logger.info(f"Found {index_stats.get('total_vectors', 0)} vectors in index")
        
        logger.info("Application startup complete!")
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise
    
    yield
    
    logger.info("Shutting down Trading Assistant application...")


async def process_and_index_data():
    """Process WhatsApp data and index it in the vector store."""
    try:
        logger.info("Processing WhatsApp chat data...")
        
        # Process data
        processor = WhatsAppDataProcessor()
        chunks = processor.process_data()
        
        if chunks:
            logger.info(f"Indexing {len(chunks)} document chunks...")
            success = vector_store.upsert_documents(chunks)
            
            if success:
                logger.info("Data processing and indexing completed successfully!")
            else:
                logger.error("Failed to index documents")
        else:
            logger.warning("No document chunks created from data processing")
            
    except Exception as e:
        logger.error(f"Error processing and indexing data: {e}")


# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="AI-powered trading assistant with RAG capabilities for funded trading support",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Trading Assistant API",
        "version": settings.app_version,
        "status": "active",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "start_conversation": "/start-conversation - POST - Start a new conversation",
            "chat": "/chat/{conversation_id} - POST - Send message in conversation",
            "legacy_chat": "/chat - POST - Legacy chat endpoint (deprecated)",
            "health": "/health - GET - Health check endpoint",
            "stats": "/stats - GET - System statistics"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check vector store connection
        vector_stats = vector_store.get_index_stats() if vector_store else {}
        
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "rag_system": "active" if rag_system else "inactive",
                "vector_store": "active" if vector_store else "inactive",
                "vector_count": vector_stats.get("total_vectors", 0)
            }
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/stats")
async def get_stats():
    """Get system statistics."""
    try:
        stats = {
            "timestamp": datetime.now().isoformat(),
            "vector_store": vector_store.get_index_stats() if vector_store else {},
            "application": {
                "name": settings.app_name,
                "version": settings.app_version,
                "debug": settings.debug
            }
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving statistics")


@app.post("/start-conversation", response_model=StartConversationResponse)
async def start_conversation(request: StartConversationRequest):
    """
    Start a new conversation and return a conversation ID.

    This endpoint creates a new conversation session and returns a unique
    conversation ID that should be used for subsequent chat messages.
    """
    try:
        import uuid

        # Generate unique conversation ID
        conversation_id = str(uuid.uuid4())

        logger.info(f"Started new conversation: {conversation_id}")

        response = StartConversationResponse(
            conversation_id=conversation_id,
            message="Hello! I'm your Trading Assistant. I'm here to help you with funded trading, prop firms, risk management, and trading strategies. What would you like to know?"
        )

        return response

    except Exception as e:
        logger.error(f"Error starting conversation: {e}")
        raise HTTPException(status_code=500, detail="Error starting conversation")


@app.post("/chat/{conversation_id}", response_model=ChatResponse)
async def chat_with_conversation(conversation_id: str, request: ChatRequest):
    """
    Send a message in an existing conversation.

    This endpoint processes trading queries within the context of an existing
    conversation, maintaining conversation history and memory.
    """
    try:
        logger.info(f"Received query in conversation {conversation_id}: {request.query[:100]}...")

        if not rag_system:
            raise HTTPException(status_code=503, detail="RAG system not initialized")

        # Validate request
        if not request.query or len(request.query.strip()) == 0:
            raise HTTPException(status_code=400, detail="Query cannot be empty")

        if len(request.query) > 2000:
            raise HTTPException(status_code=400, detail="Query too long (max 2000 characters)")

        # Create QueryRequest for the RAG system
        query_request = QueryRequest(
            query=request.query,
            session_id=conversation_id,
            user_id=None  # Could be extracted from auth if needed
        )

        # Process query
        rag_response = await rag_system.process_query(query_request)

        # Convert to ChatResponse
        response = ChatResponse(
            response=rag_response.response,
            conversation_id=conversation_id,
            sources=rag_response.sources,
            confidence=rag_response.confidence
        )

        logger.info(f"Generated response for conversation {conversation_id} with confidence: {response.confidence:.3f}")

        return response

    except Exception as e:
        logger.error(f"Error processing chat message: {e}")
        raise HTTPException(status_code=500, detail="Error processing message")


@app.post("/chat", response_model=QueryResponse, deprecated=True)
async def chat_endpoint(request: QueryRequest):
    """
    Legacy chat endpoint for trading queries (DEPRECATED).

    This endpoint is deprecated. Please use the new conversation-based endpoints:
    1. POST /start-conversation - to start a new conversation
    2. POST /chat/{conversation_id} - to send messages in a conversation

    This endpoint processes trading-related queries using RAG (Retrieval Augmented Generation)
    with conversation memory and returns helpful responses.
    """
    try:
        logger.info(f"Received query: {request.query[:100]}...")
        
        if not rag_system:
            raise HTTPException(status_code=503, detail="RAG system not initialized")
        
        # Validate request
        if not request.query or len(request.query.strip()) == 0:
            raise HTTPException(status_code=400, detail="Query cannot be empty")
        
        if len(request.query) > 2000:
            raise HTTPException(status_code=400, detail="Query too long (max 2000 characters)")
        
        # Process query
        response = await rag_system.process_query(request)
        
        logger.info(f"Generated response with confidence: {response.confidence:.3f}")
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.post("/reindex")
async def reindex_data(background_tasks: BackgroundTasks):
    """
    Reindex the data in the vector store.
    This endpoint is useful for updating the knowledge base.
    """
    try:
        if not vector_store:
            raise HTTPException(status_code=503, detail="Vector store not initialized")
        
        # Add reindexing task to background
        background_tasks.add_task(process_and_index_data)
        
        return {
            "message": "Reindexing started in background",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error starting reindex: {e}")
        raise HTTPException(status_code=500, detail="Error starting reindex process")


# Exception handlers
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.core.app:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )
