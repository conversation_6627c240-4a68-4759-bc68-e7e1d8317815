"""Memory management system for the trading assistant."""

import json
import redis
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import sqlite3
from pathlib import Path

from app.models.schemas import MemoryEntry, ConversationContext
from config import settings


class MemoryManager:
    """Manages different types of memory for the trading assistant."""
    
    def __init__(self):
        self.redis_client = self._initialize_redis()
        self.sqlite_db_path = "data/memory.db"
        self._initialize_sqlite()
        
        self.max_conversation_history = settings.max_conversation_history
        self.short_term_memory_limit = settings.short_term_memory_limit
        self.long_term_memory_limit = settings.long_term_memory_limit
    
    def _initialize_redis(self) -> redis.Redis:
        """Initialize Redis client for short-term memory."""
        try:
            client = redis.from_url(settings.redis_url)
            client.ping()  # Test connection
            print("Connected to Redis for memory management")
            return client
        except Exception as e:
            print(f"Warning: Could not connect to Redis: {e}")
            print("Using in-memory fallback for short-term memory")
            return None
    
    def _initialize_sqlite(self):
        """Initialize SQLite database for long-term memory."""
        Path("data").mkdir(exist_ok=True)
        
        conn = sqlite3.connect(self.sqlite_db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS long_term_memory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                content TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                memory_type TEXT NOT NULL,
                importance_score REAL DEFAULT 0.0,
                session_id TEXT,
                user_id TEXT,
                metadata TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversation_contexts (
                session_id TEXT PRIMARY KEY,
                user_id TEXT,
                context_data TEXT NOT NULL,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL
            )
        ''')
        
        conn.commit()
        conn.close()
        print("Initialized SQLite database for long-term memory")
    
    def store_conversation_buffer(self, session_id: str, messages: List[Dict[str, Any]]):
        """Store conversation buffer in Redis."""
        try:
            if self.redis_client:
                # Keep only recent messages
                recent_messages = messages[-self.max_conversation_history:]
                
                self.redis_client.setex(
                    f"conversation:{session_id}",
                    timedelta(hours=24),  # Expire after 24 hours
                    json.dumps(recent_messages)
                )
            else:
                # Fallback: store in memory (will be lost on restart)
                if not hasattr(self, '_memory_fallback'):
                    self._memory_fallback = {}
                self._memory_fallback[f"conversation:{session_id}"] = messages[-self.max_conversation_history:]
                
        except Exception as e:
            print(f"Error storing conversation buffer: {e}")
    
    def get_conversation_buffer(self, session_id: str) -> List[Dict[str, Any]]:
        """Retrieve conversation buffer from Redis."""
        try:
            if self.redis_client:
                data = self.redis_client.get(f"conversation:{session_id}")
                if data:
                    return json.loads(data)
            else:
                # Fallback
                if hasattr(self, '_memory_fallback'):
                    return self._memory_fallback.get(f"conversation:{session_id}", [])
            
            return []
            
        except Exception as e:
            print(f"Error retrieving conversation buffer: {e}")
            return []
    
    def store_short_term_memory(self, session_id: str, content: str, importance_score: float = 0.5):
        """Store short-term memory in Redis."""
        try:
            memory_entry = MemoryEntry(
                content=content,
                timestamp=datetime.now(),
                memory_type="short_term",
                importance_score=importance_score,
                session_id=session_id
            )
            
            if self.redis_client:
                # Get existing short-term memories
                existing_key = f"short_term:{session_id}"
                existing_data = self.redis_client.get(existing_key)
                
                if existing_data:
                    memories = json.loads(existing_data)
                else:
                    memories = []
                
                # Add new memory
                memories.append(memory_entry.dict())
                
                # Keep only recent memories
                memories = memories[-self.short_term_memory_limit:]
                
                # Store back
                self.redis_client.setex(
                    existing_key,
                    timedelta(hours=6),  # Expire after 6 hours
                    json.dumps(memories)
                )
            else:
                # Fallback
                if not hasattr(self, '_memory_fallback'):
                    self._memory_fallback = {}
                
                key = f"short_term:{session_id}"
                if key not in self._memory_fallback:
                    self._memory_fallback[key] = []
                
                self._memory_fallback[key].append(memory_entry.dict())
                self._memory_fallback[key] = self._memory_fallback[key][-self.short_term_memory_limit:]
                
        except Exception as e:
            print(f"Error storing short-term memory: {e}")
    
    def get_short_term_memory(self, session_id: str) -> List[MemoryEntry]:
        """Retrieve short-term memory from Redis."""
        try:
            if self.redis_client:
                data = self.redis_client.get(f"short_term:{session_id}")
                if data:
                    memories_data = json.loads(data)
                    return [MemoryEntry(**memory) for memory in memories_data]
            else:
                # Fallback
                if hasattr(self, '_memory_fallback'):
                    memories_data = self._memory_fallback.get(f"short_term:{session_id}", [])
                    return [MemoryEntry(**memory) for memory in memories_data]
            
            return []
            
        except Exception as e:
            print(f"Error retrieving short-term memory: {e}")
            return []
    
    def store_long_term_memory(
        self, 
        content: str, 
        importance_score: float = 0.7,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Store long-term memory in SQLite."""
        try:
            conn = sqlite3.connect(self.sqlite_db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO long_term_memory 
                (content, timestamp, memory_type, importance_score, session_id, user_id, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                content,
                datetime.now(),
                "long_term",
                importance_score,
                session_id,
                user_id,
                json.dumps(metadata) if metadata else None
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error storing long-term memory: {e}")
    
    def get_long_term_memory(
        self, 
        user_id: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[MemoryEntry]:
        """Retrieve long-term memory from SQLite."""
        try:
            conn = sqlite3.connect(self.sqlite_db_path)
            cursor = conn.cursor()
            
            if limit is None:
                limit = self.long_term_memory_limit
            
            if user_id:
                cursor.execute('''
                    SELECT content, timestamp, memory_type, importance_score, session_id, user_id
                    FROM long_term_memory 
                    WHERE user_id = ?
                    ORDER BY importance_score DESC, timestamp DESC
                    LIMIT ?
                ''', (user_id, limit))
            else:
                cursor.execute('''
                    SELECT content, timestamp, memory_type, importance_score, session_id, user_id
                    FROM long_term_memory 
                    ORDER BY importance_score DESC, timestamp DESC
                    LIMIT ?
                ''', (limit,))
            
            rows = cursor.fetchall()
            conn.close()
            
            memories = []
            for row in rows:
                memory = MemoryEntry(
                    content=row[0],
                    timestamp=datetime.fromisoformat(row[1]),
                    memory_type=row[2],
                    importance_score=row[3],
                    session_id=row[4],
                    user_id=row[5]
                )
                memories.append(memory)
            
            return memories
            
        except Exception as e:
            print(f"Error retrieving long-term memory: {e}")
            return []
    
    def get_conversation_context(self, session_id: str) -> ConversationContext:
        """Get complete conversation context."""
        try:
            # Get conversation buffer
            messages = self.get_conversation_buffer(session_id)
            
            # Get short-term memory
            short_term_memories = self.get_short_term_memory(session_id)
            short_term_content = [memory.content for memory in short_term_memories]
            
            # Get relevant long-term memory
            long_term_memories = self.get_long_term_memory(limit=10)
            long_term_content = [memory.content for memory in long_term_memories]
            
            return ConversationContext(
                session_id=session_id,
                messages=messages,
                short_term_memory=short_term_content,
                long_term_memory=long_term_content
            )
            
        except Exception as e:
            print(f"Error getting conversation context: {e}")
            return ConversationContext(session_id=session_id)
