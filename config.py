"""Configuration settings for the Trading Assistant application."""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Google Gemini API Configuration
    google_api_key: str = Field(..., env="GOOGLE_API_KEY")
    
    # Pinecone Configuration
    pinecone_api_key: str = Field(..., env="PINECONE_API_KEY")
    pinecone_environment: str = Field(..., env="PINECONE_ENVIRONMENT")
    pinecone_index_name: str = Field(default="trading-assistant-index", env="PINECONE_INDEX_NAME")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # Application Configuration
    app_name: str = Field(default="Trading Assistant", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=True, env="DEBUG")
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # Data Configuration
    data_file_path: str = Field(
        default="WhatsApp Chat with Queries and SOCIALS AWAAM.txt", 
        env="DATA_FILE_PATH"
    )
    chunk_size: int = Field(default=1000, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=200, env="CHUNK_OVERLAP")
    
    # Memory Configuration
    max_conversation_history: int = Field(default=50, env="MAX_CONVERSATION_HISTORY")
    short_term_memory_limit: int = Field(default=10, env="SHORT_TERM_MEMORY_LIMIT")
    long_term_memory_limit: int = Field(default=100, env="LONG_TERM_MEMORY_LIMIT")
    
    # Embedding Configuration
    embedding_model: str = Field(default="all-MiniLM-L6-v2", env="EMBEDDING_MODEL")
    embedding_dimension: int = Field(default=384, env="EMBEDDING_DIMENSION")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()
