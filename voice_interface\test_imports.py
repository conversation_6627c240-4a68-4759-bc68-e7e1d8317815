"""Test script to verify all imports work correctly."""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """Test all imports."""
    print("Testing imports...")
    
    try:
        print("✅ Testing speech_recognition...")
        import speech_recognition as sr
        
        print("✅ Testing pyttsx3...")
        import pyttsx3
        
        print("✅ Testing pyaudio...")
        import pyaudio
        
        print("✅ Testing voice_input...")
        from voice_interface.voice_input import VoiceInputHandler
        
        print("✅ Testing voice_output...")
        from voice_interface.voice_output import VoiceOutputHandler
        
        print("✅ Testing basic app imports...")
        from app.models.schemas import QueryRequest, QueryResponse

        print("⚠️  Skipping full RAG system test (requires additional setup)")
        # from app.rag.langgraph_rag import TradingAssistantRAG
        # from app.services.vector_store import PineconeVectorStore
        # from voice_interface.voice_conversation import VoiceConversationManager
        
        print("✅ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality without requiring microphone input."""
    print("\nTesting basic functionality...")
    
    try:
        # Test voice output
        print("🔊 Testing voice output...")
        from voice_interface.voice_output import VoiceOutputHandler
        voice_output = VoiceOutputHandler()
        print("✅ Voice output initialized")
        
        # Test voice input (just initialization)
        print("🎤 Testing voice input initialization...")
        from voice_interface.voice_input import VoiceInputHandler
        voice_input = VoiceInputHandler()
        print("✅ Voice input initialized")
        
        print("✅ Basic functionality test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Functionality test error: {e}")
        return False

if __name__ == "__main__":
    print("Voice Interface Test Suite")
    print("=" * 40)
    
    if test_imports():
        if test_basic_functionality():
            print("\n🎉 All tests passed! Voice interface is ready to use.")
        else:
            print("\n❌ Functionality tests failed.")
    else:
        print("\n❌ Import tests failed.")
