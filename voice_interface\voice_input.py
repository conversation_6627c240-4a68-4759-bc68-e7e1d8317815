"""Voice input handler using speech recognition."""

import speech_recognition as sr
import logging
from typing import Optional
import time

logger = logging.getLogger(__name__)


class VoiceInputHandler:
    """Handles voice input using speech recognition."""
    
    def __init__(self, timeout: int = 10, phrase_timeout: int = 5, pause_threshold: float = 1.0):
        """
        Initialize voice input handler.

        Args:
            timeout: Maximum time to wait for speech to start
            phrase_timeout: Maximum time to wait for phrase to complete
            pause_threshold: Minimum pause duration to consider speech complete
        """
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.timeout = timeout
        self.phrase_timeout = phrase_timeout
        self.pause_threshold = pause_threshold

        # Adjust recognizer settings for better speech detection
        self.recognizer.energy_threshold = 300  # Minimum audio energy to consider for recording
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = pause_threshold  # Seconds of non-speaking audio before phrase is complete

        # Adjust for ambient noise
        self._calibrate_microphone()
    
    def _calibrate_microphone(self):
        """Calibrate microphone for ambient noise."""
        try:
            print("🎤 Calibrating microphone for ambient noise...")
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            print("✅ Microphone calibrated successfully")
        except Exception as e:
            logger.error(f"Error calibrating microphone: {e}")
            print("⚠️  Warning: Could not calibrate microphone")
    
    def listen_for_complete_speech(self) -> Optional[str]:
        """
        Listen for complete speech with proper pause detection.

        Returns:
            Recognized text or None if no speech detected
        """
        try:
            print("🎤 Listening... (speak your complete question, I'll wait for you to finish)")

            with self.microphone as source:
                # Listen for audio with longer timeout and no phrase limit
                # This allows for longer, more complete sentences
                audio = self.recognizer.listen(
                    source,
                    timeout=self.timeout,
                    phrase_time_limit=None  # No limit on phrase length
                )

            print("🔄 Processing your complete question...")

            # Use Google's free speech recognition service
            text = self.recognizer.recognize_google(audio)
            print(f"📝 You said: {text}")

            # Validate that we got a reasonable amount of speech
            if len(text.strip()) < 3:
                print("⚠️  That seemed too short. Please try speaking your full question.")
                return None

            return text

        except sr.WaitTimeoutError:
            print("⏰ No speech detected. Please try speaking closer to the microphone.")
            return None
        except sr.UnknownValueError:
            print("❓ Could not understand the speech. Please speak more clearly.")
            return None
        except sr.RequestError as e:
            logger.error(f"Speech recognition service error: {e}")
            print(f"❌ Speech recognition service error: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in speech recognition: {e}")
            print(f"❌ Unexpected error: {e}")
            return None

    def listen_for_speech(self) -> Optional[str]:
        """
        Legacy method - now uses complete speech detection.

        Returns:
            Recognized text or None if no speech detected
        """
        return self.listen_for_complete_speech()
    
    def listen_with_confirmation(self) -> Optional[str]:
        """
        Listen for speech with confirmation to ensure complete input.

        Returns:
            Recognized text or None if no speech detected
        """
        max_attempts = 2

        for attempt in range(max_attempts):
            try:
                if attempt == 0:
                    print("🎤 Please speak your complete question (I'll wait for you to finish)...")
                else:
                    print("🎤 Let me try again. Please speak your complete question...")

                with self.microphone as source:
                    # Listen with longer timeout and better settings
                    audio = self.recognizer.listen(
                        source,
                        timeout=15,  # 15 seconds to start speaking
                        phrase_time_limit=None  # No limit on phrase length
                    )

                print("🔄 Processing your question...")

                # Use Google's speech recognition
                text = self.recognizer.recognize_google(audio)

                # Validate the input
                if len(text.strip()) < 3:
                    print("⚠️  That seemed too short. Please try again with your full question.")
                    continue

                print(f"📝 I heard: '{text}'")

                # Ask for confirmation if the text seems incomplete or unclear
                if len(text.split()) < 3 or text.endswith(('what', 'how', 'why', 'when', 'where')):
                    print("🤔 That might be incomplete. If you want to continue with this, say 'yes'. Otherwise, I'll listen again.")

                    # Quick confirmation listen
                    try:
                        with self.microphone as source:
                            confirm_audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=3)
                        confirm_text = self.recognizer.recognize_google(confirm_audio).lower()

                        if 'yes' in confirm_text or 'okay' in confirm_text or 'ok' in confirm_text:
                            return text
                        else:
                            print("👍 Let me listen for your complete question again...")
                            continue
                    except:
                        # If confirmation fails, just proceed with what we have
                        pass

                return text

            except sr.WaitTimeoutError:
                print("⏰ No speech detected. Please try speaking closer to the microphone.")
                if attempt == max_attempts - 1:
                    return None
                continue

            except sr.UnknownValueError:
                print("❓ Could not understand the speech. Please speak more clearly.")
                if attempt == max_attempts - 1:
                    return None
                continue

            except sr.RequestError as e:
                logger.error(f"Speech recognition service error: {e}")
                print(f"❌ Speech recognition service error: {e}")
                return None

            except Exception as e:
                logger.error(f"Unexpected error in speech recognition: {e}")
                print(f"❌ Unexpected error: {e}")
                if attempt == max_attempts - 1:
                    return None
                continue

        return None

    def listen_continuously(self, callback_func, processing_check_func=None):
        """
        Listen continuously for speech and call callback function.

        Args:
            callback_func: Function to call with recognized text
            processing_check_func: Function to check if system is processing (returns True if processing)
        """
        print("🎤 Starting continuous listening mode...")
        print("💡 Say 'stop listening' or 'quit' to exit")
        print("💡 I'll wait for you to finish speaking before processing your question")

        while True:
            try:
                # Check if system is processing before listening
                if processing_check_func and processing_check_func():
                    print("⏳ Please wait, I'm still processing your previous question...")
                    time.sleep(2)  # Wait 2 seconds before checking again
                    continue

                # Use the improved listening method
                text = self.listen_with_confirmation()

                if text:
                    # Check for exit commands
                    if any(cmd in text.lower() for cmd in ['stop listening', 'quit', 'exit', 'goodbye']):
                        print("👋 Stopping voice input...")
                        break

                    # Call the callback function with recognized text
                    callback_func(text)

                # Small delay between listening sessions
                time.sleep(1)

            except KeyboardInterrupt:
                print("\n👋 Voice input stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in continuous listening: {e}")
                print(f"❌ Error: {e}")
                time.sleep(1)  # Wait before retrying
    
    def test_microphone(self) -> bool:
        """
        Test if microphone is working.
        
        Returns:
            True if microphone is working, False otherwise
        """
        try:
            print("🧪 Testing microphone... Please say something")
            text = self.listen_for_speech()
            
            if text:
                print(f"✅ Microphone test successful! Heard: {text}")
                return True
            else:
                print("❌ Microphone test failed - no speech detected")
                return False
                
        except Exception as e:
            logger.error(f"Microphone test error: {e}")
            print(f"❌ Microphone test error: {e}")
            return False


def main():
    """Test the voice input handler."""
    print("Voice Input Handler Test")
    print("=" * 30)
    
    handler = VoiceInputHandler()
    
    # Test microphone
    if not handler.test_microphone():
        print("❌ Microphone test failed. Please check your microphone setup.")
        return
    
    # Test single recognition
    print("\n🎤 Single recognition test:")
    text = handler.listen_for_speech()
    if text:
        print(f"✅ Success: {text}")
    else:
        print("❌ No speech recognized")


if __name__ == "__main__":
    main()
