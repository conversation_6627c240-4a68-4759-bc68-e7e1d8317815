# Google Gemini API Configuration
GOOGLE_API_KEY=AIzaSyB0GJg5AKMbhSoxuL3ROJxIH_dbnvQmQQI

# Pinecone Configuration
PINECONE_API_KEY=pcsk_5N42Q1_5BBvC8kivAGh2XcKsVx4uNNBaVBGWj5Y84jaP96BwNBoWCL9StmWfTKPTdBDifR
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=trading-assistant-index

# Redis Configuration (for memory)
REDIS_URL=redis://localhost:6379/0

# Application Configuration
APP_NAME=Trading Assistant
APP_VERSION=1.0.0
DEBUG=True
HOST=0.0.0.0
PORT=8000

# Data Configuration
DATA_FILE_PATH=WhatsApp Chat with Queries and SOCIALS AWAAM.txt
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Memory Configuration
MAX_CONVERSATION_HISTORY=50
SHORT_TERM_MEMORY_LIMIT=10
LONG_TERM_MEMORY_LIMIT=100
