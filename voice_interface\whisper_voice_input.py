"""Whisper-based voice input handler for better speech recognition."""

import whisper
import pyaudio
import wave
import tempfile
import os
import logging
from typing import Optional
import time
import numpy as np

logger = logging.getLogger(__name__)


class WhisperVoiceInputHandler:
    """Handles voice input using OpenAI Whisper for better accuracy."""
    
    def __init__(self, model_size: str = "base", language: str = "en"):
        """
        Initialize Whisper voice input handler.
        
        Args:
            model_size: Whisper model size ("tiny", "base", "small", "medium", "large")
            language: Language code for recognition (e.g., "en" for English)
        """
        self.model_size = model_size
        self.language = language
        self.model = None
        
        # Audio recording settings
        self.sample_rate = 16000  # Whisper expects 16kHz
        self.chunk_size = 1024
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # Voice activity detection settings
        self.silence_threshold = 500  # Adjust based on your microphone
        self.silence_duration = 2.0  # Seconds of silence to stop recording
        self.max_recording_time = 30.0  # Maximum recording time in seconds
        
        self.audio = pyaudio.PyAudio()
        
        # Initialize Whisper model
        self._load_whisper_model()
    
    def _load_whisper_model(self):
        """Load the Whisper model."""
        try:
            print(f"🧠 Loading Whisper model ({self.model_size})...")
            print("⏳ This may take a moment on first run...")
            
            self.model = whisper.load_model(self.model_size)
            print(f"✅ Whisper model ({self.model_size}) loaded successfully!")
            
        except Exception as e:
            logger.error(f"Error loading Whisper model: {e}")
            print(f"❌ Error loading Whisper model: {e}")
            self.model = None
    
    def _is_silent(self, audio_chunk):
        """Check if audio chunk is silent."""
        # Convert bytes to numpy array
        audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
        # Calculate RMS (Root Mean Square) for volume level
        rms = np.sqrt(np.mean(audio_data**2))
        return rms < self.silence_threshold
    
    def _record_audio_with_vad(self) -> Optional[str]:
        """
        Record audio with voice activity detection.
        
        Returns:
            Path to recorded audio file or None if failed
        """
        try:
            print("🎤 Listening... (speak your complete question)")
            print("💡 I'll automatically stop when you finish speaking")
            
            # Create temporary file for audio
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
            temp_filename = temp_file.name
            temp_file.close()
            
            # Open audio stream
            stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            frames = []
            silent_chunks = 0
            recording_started = False
            start_time = time.time()
            
            print("🔴 Recording started...")
            
            while True:
                # Read audio chunk
                chunk = stream.read(self.chunk_size)
                
                # Check if we've been recording too long
                if time.time() - start_time > self.max_recording_time:
                    print("⏰ Maximum recording time reached")
                    break
                
                # Check if chunk has speech
                is_silent = self._is_silent(chunk)
                
                if not is_silent:
                    # Speech detected
                    if not recording_started:
                        print("🎙️  Speech detected, recording...")
                        recording_started = True
                    frames.append(chunk)
                    silent_chunks = 0
                elif recording_started:
                    # Silence after speech started
                    frames.append(chunk)
                    silent_chunks += 1
                    
                    # Check if we've had enough silence to stop
                    silence_time = silent_chunks * self.chunk_size / self.sample_rate
                    if silence_time >= self.silence_duration:
                        print("✅ Speech completed, processing...")
                        break
            
            stream.stop_stream()
            stream.close()
            
            if not frames:
                print("❌ No speech detected")
                os.unlink(temp_filename)
                return None
            
            # Save recorded audio to file
            with wave.open(temp_filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(self.audio.get_sample_size(self.format))
                wf.setframerate(self.sample_rate)
                wf.writeframes(b''.join(frames))
            
            print(f"🔊 Recorded {len(frames)} audio chunks")
            return temp_filename
            
        except Exception as e:
            logger.error(f"Error recording audio: {e}")
            print(f"❌ Error recording audio: {e}")
            return None
    
    def listen_for_speech(self) -> Optional[str]:
        """
        Listen for speech and convert to text using Whisper.
        
        Returns:
            Recognized text or None if no speech detected
        """
        if not self.model:
            print("❌ Whisper model not loaded")
            return None
        
        try:
            # Record audio
            audio_file = self._record_audio_with_vad()
            if not audio_file:
                return None
            
            print("🧠 Processing with Whisper...")
            
            # Transcribe with Whisper
            result = self.model.transcribe(
                audio_file,
                language=self.language,
                fp16=False,  # Use fp32 for better compatibility
                verbose=False
            )
            
            # Clean up temporary file
            os.unlink(audio_file)
            
            text = result["text"].strip()
            
            if not text:
                print("❌ No text recognized")
                return None
            
            print(f"📝 Whisper recognized: {text}")
            
            # Validate minimum length
            if len(text) < 3:
                print("⚠️  Text too short, please try again")
                return None
            
            return text
            
        except Exception as e:
            logger.error(f"Error in Whisper speech recognition: {e}")
            print(f"❌ Error in speech recognition: {e}")
            return None
    
    def listen_continuously(self, callback_func, processing_check_func=None):
        """
        Listen continuously for speech and call callback function.
        
        Args:
            callback_func: Function to call with recognized text
            processing_check_func: Function to check if system is processing
        """
        print("🎤 Starting Whisper continuous listening mode...")
        print("💡 Say 'stop listening' or 'quit' to exit")
        print("💡 I'll automatically detect when you finish speaking")
        
        while True:
            try:
                # Check if system is processing
                if processing_check_func and processing_check_func():
                    print("⏳ Please wait, I'm still processing your previous question...")
                    time.sleep(2)
                    continue
                
                # Listen for speech
                text = self.listen_for_speech()
                
                if text:
                    # Check for exit commands
                    if any(cmd in text.lower() for cmd in ['stop listening', 'quit', 'exit', 'goodbye']):
                        print("👋 Stopping voice input...")
                        break
                    
                    # Call callback function
                    callback_func(text)
                
                # Small delay between sessions
                time.sleep(0.5)
                
            except KeyboardInterrupt:
                print("\n👋 Voice input stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in continuous listening: {e}")
                print(f"❌ Error: {e}")
                time.sleep(1)
    
    def test_microphone(self) -> bool:
        """
        Test microphone and Whisper recognition.
        
        Returns:
            True if test successful, False otherwise
        """
        try:
            print("🧪 Testing microphone and Whisper recognition...")
            print("Please say 'hello this is a test'")
            
            text = self.listen_for_speech()
            
            if text:
                print(f"✅ Test successful! Recognized: {text}")
                return True
            else:
                print("❌ Test failed - no speech recognized")
                return False
                
        except Exception as e:
            logger.error(f"Microphone test error: {e}")
            print(f"❌ Test error: {e}")
            return False
    
    def cleanup(self):
        """Clean up audio resources."""
        try:
            self.audio.terminate()
        except:
            pass


def main():
    """Test the Whisper voice input handler."""
    print("Whisper Voice Input Handler Test")
    print("=" * 40)
    
    handler = WhisperVoiceInputHandler(model_size="base")
    
    try:
        # Test microphone
        if handler.test_microphone():
            print("\n✅ Whisper voice input is working!")
        else:
            print("\n❌ Whisper voice input test failed")
    finally:
        handler.cleanup()


if __name__ == "__main__":
    main()
