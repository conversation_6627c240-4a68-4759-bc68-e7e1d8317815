"""Voice conversation manager that integrates voice I/O with RAG system."""

import asyncio
import uuid
import logging
from typing import Optional, Dict, Any
from datetime import datetime

# Import existing components
from app.rag.langgraph_rag import TradingAssistantRAG
from app.services.vector_store import PineconeVectorStore
from app.models.schemas import QueryRequest, QueryResponse
from voice_interface.whisper_voice_input import WhisperVoiceInputHandler
from voice_interface.voice_output import VoiceOutputHandler

logger = logging.getLogger(__name__)


class VoiceConversationManager:
    """Manages voice-based conversations using existing RAG system."""
    
    def __init__(self):
        """Initialize the voice conversation manager."""
        self.conversation_id = None
        self.rag_system = None
        self.vector_store = None
        self.voice_input = None
        self.voice_output = None
        self.is_running = False
        self.is_processing = False
        self.conversation_history = []
        
    async def initialize(self):
        """Initialize all components."""
        try:
            print("🚀 Initializing Voice Conversation Manager...")
            
            # Initialize vector store
            print("📊 Initializing vector store...")
            self.vector_store = PineconeVectorStore()
            
            # Initialize RAG system
            print("🧠 Initializing RAG system...")
            self.rag_system = TradingAssistantRAG()
            
            # Initialize voice components
            print("🎤 Initializing Whisper voice input...")
            self.voice_input = WhisperVoiceInputHandler(
                model_size="base",  # Good balance of speed and accuracy
                language="en"  # English language
            )
            
            print("🔊 Initializing voice output...")
            self.voice_output = VoiceOutputHandler(rate=180, volume=0.9)
            
            # Generate conversation ID
            self.conversation_id = str(uuid.uuid4())
            
            print(f"✅ Voice Conversation Manager initialized successfully!")
            print(f"📝 Conversation ID: {self.conversation_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing voice conversation manager: {e}")
            print(f"❌ Error initializing: {e}")
            return False
    
    async def process_voice_query(self, query_text: str) -> Optional[str]:
        """
        Process a voice query through the RAG system.

        Args:
            query_text: The recognized speech text

        Returns:
            Response text or None if error
        """
        try:
            if not self.rag_system:
                print("❌ RAG system not initialized")
                return "Sorry, the system is not properly initialized."

            print(f"🧠 Processing query: {query_text[:100]}...")

            # Create query request
            query_request = QueryRequest(
                query=query_text,
                session_id=self.conversation_id,
                user_id=None
            )

            # Process through RAG system with timeout handling
            try:
                response = await asyncio.wait_for(
                    self.rag_system.process_query(query_request),
                    timeout=20.0  # 20 second timeout for RAG processing
                )

                # Store in conversation history
                self.conversation_history.append({
                    "timestamp": datetime.now().isoformat(),
                    "user": query_text,
                    "assistant": response.response,
                    "confidence": response.confidence,
                    "sources": len(response.sources)
                })

                print(f"✅ Response generated (confidence: {response.confidence:.3f})")
                return response.response

            except asyncio.TimeoutError:
                print("⏰ RAG processing timed out")
                return "Sorry, that question took too long to process. Please try asking something simpler about trading."

        except Exception as e:
            logger.error(f"Error processing voice query: {e}")
            print(f"❌ Error processing query: {e}")
            return "Sorry, I encountered an error processing your question. Please try again."
    
    def handle_voice_input(self, recognized_text: str):
        """
        Handle recognized voice input.

        Args:
            recognized_text: Text recognized from speech
        """
        try:
            print(f"👤 User said: {recognized_text}")

            # Check for special commands
            if self._handle_special_commands(recognized_text):
                return

            # Set processing flag to prevent new input
            self.is_processing = True
            print("🔄 Processing your question, please wait...")

            # Process the query in a separate thread to avoid event loop issues
            import threading
            def process_in_thread():
                try:
                    # Create new event loop for this thread
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)

                    # Add timeout to prevent hanging
                    try:
                        new_loop.run_until_complete(
                            asyncio.wait_for(
                                self._process_and_respond(recognized_text),
                                timeout=30.0  # 30 second timeout
                            )
                        )
                    except asyncio.TimeoutError:
                        print("⏰ Query processing timed out after 30 seconds")
                        error_msg = "Sorry, that took too long to process. Please try a simpler question."
                        self.voice_output.speak(error_msg, wait=True)

                    new_loop.close()
                    # Clear processing flag when done
                    self.is_processing = False
                    print("✅ Ready for your next question...")
                except Exception as e:
                    logger.error(f"Error in thread processing: {e}")
                    print(f"❌ Error processing query: {e}")
                    # Speak error message
                    try:
                        error_msg = "Sorry, I encountered an error. Please try again."
                        self.voice_output.speak(error_msg, wait=True)
                    except:
                        pass
                    self.is_processing = False

            thread = threading.Thread(target=process_in_thread)
            thread.daemon = True
            thread.start()

        except Exception as e:
            logger.error(f"Error handling voice input: {e}")
            print(f"❌ Error handling voice input: {e}")
            self.is_processing = False
    
    async def _process_and_respond(self, query_text: str):
        """Process query and provide voice response."""
        try:
            print("🧠 Generating response...")

            # Get response from RAG system
            response_text = await self.process_voice_query(query_text)

            if response_text:
                # Display the response
                print(f"🤖 Assistant: {response_text[:200]}{'...' if len(response_text) > 200 else ''}")
                print("🔊 Speaking response, please wait...")

                # Use synchronous speech to ensure it completes
                success = self.voice_output.speak(response_text, wait=True)
                if success:
                    print("✅ Response completed")
                else:
                    print("❌ Failed to speak response - check your audio settings")
                    # Try to speak a shorter error message
                    self.voice_output.speak("Sorry, there was an audio issue.", wait=True)
            else:
                error_msg = "I'm sorry, I couldn't process your request. Please try again."
                print(f"🤖 Assistant: {error_msg}")
                print("🔊 Speaking error message...")
                self.voice_output.speak(error_msg, wait=True)

        except Exception as e:
            logger.error(f"Error in process and respond: {e}")
            print(f"❌ Error in process and respond: {e}")
            error_msg = "I encountered an error processing your request."
            print("🔊 Speaking error message...")
            try:
                self.voice_output.speak(error_msg, wait=True)
            except Exception as tts_error:
                print(f"❌ TTS also failed: {tts_error}")
                print("💡 Please check your audio settings")
    
    def _handle_special_commands(self, text: str) -> bool:
        """
        Handle special voice commands.
        
        Args:
            text: Recognized text
            
        Returns:
            True if special command was handled, False otherwise
        """
        text_lower = text.lower()
        
        # Stop/quit commands
        if any(cmd in text_lower for cmd in ['stop listening', 'quit', 'exit', 'goodbye']):
            self.stop_conversation()
            return True
        
        # Help command
        if 'help' in text_lower or 'what can you do' in text_lower:
            help_text = ("I'm your Trading Assistant. I can help you with funded trading, "
                        "prop firms, risk management, and trading strategies. "
                        "Just ask me any trading-related question!")
            print(f"🤖 Assistant: {help_text}")
            self.voice_output.speak(help_text, wait=False)
            return True
        
        # Repeat command
        if 'repeat' in text_lower or 'say that again' in text_lower:
            if self.conversation_history:
                last_response = self.conversation_history[-1]['assistant']
                print(f"🤖 Assistant (repeating): {last_response}")
                self.voice_output.speak(last_response, wait=False)
            else:
                msg = "I haven't said anything yet. Please ask me a question!"
                print(f"🤖 Assistant: {msg}")
                self.voice_output.speak(msg, wait=False)
            return True
        
        # Volume commands
        if 'speak louder' in text_lower or 'volume up' in text_lower:
            current_volume = self.voice_output.volume
            new_volume = min(1.0, current_volume + 0.2)
            self.voice_output.set_volume(new_volume)
            self.voice_output.speak("Volume increased", wait=False)
            return True
        
        if 'speak quieter' in text_lower or 'volume down' in text_lower:
            current_volume = self.voice_output.volume
            new_volume = max(0.1, current_volume - 0.2)
            self.voice_output.set_volume(new_volume)
            self.voice_output.speak("Volume decreased", wait=False)
            return True
        
        # Speed commands
        if 'speak faster' in text_lower:
            current_rate = self.voice_output.rate
            new_rate = min(300, current_rate + 30)
            self.voice_output.set_rate(new_rate)
            self.voice_output.speak("Speaking faster now", wait=False)
            return True
        
        if 'speak slower' in text_lower:
            current_rate = self.voice_output.rate
            new_rate = max(100, current_rate - 30)
            self.voice_output.set_rate(new_rate)
            self.voice_output.speak("Speaking slower now", wait=False)
            return True
        
        return False
    
    async def start_conversation(self):
        """Start the voice conversation."""
        if not await self.initialize():
            print("❌ Failed to initialize. Cannot start conversation.")
            return
        
        self.is_running = True
        
        # Welcome message
        welcome_msg = ("Hello! I'm your Trading Assistant. I can help you with funded trading, "
                      "prop firms, risk management, and trading strategies. "
                      "I'll wait for you to finish speaking completely before processing your question. "
                      "Take your time and speak your full question. Say 'help' for commands or 'quit' to exit.")

        print(f"🤖 Assistant: {welcome_msg}")
        print("🔊 Speaking welcome message...")
        success = self.voice_output.speak(welcome_msg, wait=True)
        if not success:
            print("❌ Audio issue detected. Please check your speakers/headphones.")
            return
        
        print("\n" + "="*60)
        print("🎤 VOICE CONVERSATION STARTED")
        print("💡 Commands: 'help', 'repeat', 'volume up/down', 'speak faster/slower', 'quit'")
        print("="*60)
        
        # Start continuous listening with processing check
        try:
            self.voice_input.listen_continuously(
                self.handle_voice_input,
                processing_check_func=lambda: self.is_processing
            )
        except KeyboardInterrupt:
            print("\n👋 Conversation interrupted by user")
        finally:
            self.stop_conversation()
    
    def stop_conversation(self):
        """Stop the voice conversation."""
        if self.is_running:
            self.is_running = False
            
            # Stop any ongoing speech
            if self.voice_output:
                self.voice_output.stop_speaking()
            
            # Goodbye message
            goodbye_msg = "Goodbye! Thank you for using the Trading Assistant."
            print(f"🤖 Assistant: {goodbye_msg}")
            if self.voice_output:
                self.voice_output.speak(goodbye_msg, wait=True)
            
            print("\n" + "="*60)
            print("👋 VOICE CONVERSATION ENDED")
            if self.conversation_history:
                print(f"📊 Total exchanges: {len(self.conversation_history)}")
            print("="*60)
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get conversation summary."""
        return {
            "conversation_id": self.conversation_id,
            "total_exchanges": len(self.conversation_history),
            "history": self.conversation_history,
            "is_running": self.is_running
        }
