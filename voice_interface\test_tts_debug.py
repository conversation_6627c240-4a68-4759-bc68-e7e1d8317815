"""Debug script to test TTS functionality in isolation."""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_basic_pyttsx3():
    """Test basic pyttsx3 functionality."""
    try:
        print("Testing basic pyttsx3...")
        
        import pyttsx3
        
        print("Creating pyttsx3 engine...")
        engine = pyttsx3.init()
        
        print("Getting voices...")
        voices = engine.getProperty('voices')
        print(f"Found {len(voices) if voices else 0} voices:")
        
        if voices:
            for i, voice in enumerate(voices):
                print(f"  {i}: {voice.name} ({voice.id})")
        
        print("Setting properties...")
        engine.setProperty('rate', 150)
        engine.setProperty('volume', 0.9)
        
        print("Testing speech...")
        engine.say("Hello, this is a basic pyttsx3 test.")
        engine.runAndWait()
        
        print("✅ Basic pyttsx3 test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Basic pyttsx3 test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_voice():
    """Test enhanced voice handler."""
    try:
        print("\nTesting enhanced voice handler...")
        
        from voice_interface.openvoice_output import OpenVoiceOutputHandler
        
        print("Creating enhanced voice handler...")
        handler = OpenVoiceOutputHandler()
        
        if not handler.is_initialized:
            print("❌ Enhanced voice handler not initialized")
            return False
        
        print("Testing enhanced voice speech...")
        success = handler.speak("Hello, this is an enhanced voice test.", wait=True)
        
        if success:
            print("✅ Enhanced voice test successful!")
        else:
            print("❌ Enhanced voice test failed!")
        
        return success
        
    except Exception as e:
        print(f"❌ Enhanced voice test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_windows_sapi():
    """Test Windows SAPI directly."""
    try:
        print("\nTesting Windows SAPI directly...")
        
        import platform
        if platform.system() != "Windows":
            print("⚠️  Not on Windows, skipping SAPI test")
            return True
        
        try:
            import win32com.client
            
            print("Creating SAPI voice object...")
            voice = win32com.client.Dispatch("SAPI.SpVoice")
            
            print("Testing SAPI speech...")
            voice.Speak("Hello, this is a Windows SAPI test.")
            
            print("✅ Windows SAPI test successful!")
            return True
            
        except ImportError:
            print("⚠️  pywin32 not available, skipping SAPI test")
            return True
            
    except Exception as e:
        print(f"❌ Windows SAPI test failed: {e}")
        return False

if __name__ == "__main__":
    print("TTS Debug Test Suite")
    print("=" * 40)
    
    # Test basic pyttsx3
    basic_success = test_basic_pyttsx3()
    
    # Test enhanced voice
    enhanced_success = test_enhanced_voice()
    
    # Test Windows SAPI
    sapi_success = test_windows_sapi()
    
    print("\n" + "=" * 40)
    print("Test Results:")
    print(f"Basic pyttsx3: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"Enhanced voice: {'✅ PASS' if enhanced_success else '❌ FAIL'}")
    print(f"Windows SAPI: {'✅ PASS' if sapi_success else '❌ FAIL'}")
    
    if basic_success and enhanced_success:
        print("\n🎉 TTS is working correctly!")
    else:
        print("\n❌ TTS has issues. Check the debug output above.")
        print("\nTroubleshooting tips:")
        print("1. Make sure your speakers/headphones are working")
        print("2. Check Windows audio settings")
        print("3. Try running as administrator")
        print("4. Check if other applications can play audio")
