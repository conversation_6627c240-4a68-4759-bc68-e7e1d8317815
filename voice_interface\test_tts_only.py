"""Test TTS functionality in isolation."""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_tts():
    """Test text-to-speech functionality."""
    try:
        print("Testing TTS functionality...")
        
        from voice_interface.voice_output import VoiceOutputHandler
        
        print("Creating VoiceOutputHandler...")
        handler = VoiceOutputHandler()
        
        print("Testing simple speech...")
        success = handler.speak("Hello, this is a test of text to speech.", wait=True)
        
        if success:
            print("✅ TTS test successful!")
        else:
            print("❌ TTS test failed!")
            
        return success
        
    except Exception as e:
        print(f"❌ Error testing TTS: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("TTS Isolation Test")
    print("=" * 30)
    
    if test_tts():
        print("\n🎉 TTS is working correctly!")
    else:
        print("\n❌ TTS has issues. Check the error messages above.")
