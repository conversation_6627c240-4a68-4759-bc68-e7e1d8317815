# Trading Assistant - RAG-Powered AI Assistant

A sophisticated AI-powered trading assistant built with RAG (Retrieval Augmented Generation) using LangGraph, FastAPI, and Gemini Flash 1.5. This assistant helps funded trading users with their queries by leveraging a comprehensive knowledge base of trading conversations and expert insights.

## 🚀 Features

- **RAG-Powered Responses**: Uses Retrieval Augmented Generation for accurate, context-aware answers
- **LangGraph Workflow**: Advanced conversation flow management with state-based processing
- **Multi-Layer Memory System**:
  - Conversation buffer memory (Redis)
  - Short-term memory for session context
  - Long-term memory for user preferences (SQLite)
- **Vector Search**: Pinecone vector database for semantic document retrieval
- **Gemini Flash 1.5**: Google's latest LLM for high-quality response generation
- **FastAPI Backend**: High-performance async API with automatic documentation
- **Comprehensive Prompt Engineering**: Specialized prompts for trading assistance

## 🏗️ Architecture

```
├── app/
│   ├── core/           # FastAPI application
│   ├── models/         # Pydantic schemas
│   ├── services/       # Vector store and external services
│   ├── utils/          # Data processing utilities
│   ├── memory/         # Memory management system
│   └── rag/            # RAG system with LangGraph
├── data/               # Data storage
├── logs/               # Application logs
├── config.py           # Configuration settings
├── main.py             # Application entry point
└── setup_data.py       # Data processing script
```

## 📋 Prerequisites

- Python 3.8+
- Google Gemini API key
- Pinecone account and API key
- Redis server (optional, falls back to in-memory storage)

## 🛠️ Installation

1. **Clone and navigate to the project:**
   ```bash
   cd tr-ass
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your API keys:
   ```env
   GOOGLE_API_KEY=your_gemini_api_key_here
   PINECONE_API_KEY=your_pinecone_api_key_here
   PINECONE_ENVIRONMENT=your_pinecone_environment
   PINECONE_INDEX_NAME=trading-assistant-index
   REDIS_URL=redis://localhost:6379/0
   ```

4. **Process and index your data:**
   ```bash
   python setup_data.py
   ```

5. **Start the application:**
   ```bash
   python main.py
   ```

## 🔧 Configuration

Key configuration options in `config.py`:

- **Data Processing**: Chunk size, overlap, and file paths
- **Memory Settings**: Conversation history limits, memory retention
- **Vector Store**: Embedding model, dimensions, and search parameters
- **API Settings**: Host, port, and debug mode

## 📡 API Usage

### Main Chat Endpoint

**POST** `/chat`

```json
{
  "query": "How do I manage drawdown in a funded trading account?",
  "session_id": "optional-session-id",
  "user_id": "optional-user-id"
}
```

**Response:**
```json
{
  "response": "To manage drawdown in a funded trading account...",
  "session_id": "session-123",
  "sources": ["chunk_1", "chunk_5"],
  "confidence": 0.85,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Other Endpoints

- **GET** `/` - API information
- **GET** `/health` - Health check
- **GET** `/stats` - System statistics
- **POST** `/reindex` - Reprocess and reindex data

## 🧠 Memory System

The assistant uses a three-tier memory system:

1. **Conversation Buffer**: Recent messages stored in Redis (24h expiry)
2. **Short-term Memory**: Important context from current session (6h expiry)
3. **Long-term Memory**: User preferences and high-value interactions (persistent)

## 🔍 RAG Workflow

The LangGraph-based RAG system follows this workflow:

1. **Load Context** - Retrieve conversation history and memory
2. **Classify Query** - Categorize the trading query type
3. **Retrieve Documents** - Find relevant information from vector store
4. **Generate Response** - Create contextual response using Gemini
5. **Update Memory** - Store important information for future use

## 📊 Data Processing

The system processes WhatsApp chat data by:

1. Parsing message timestamps, senders, and content
2. Filtering for trading-related conversations
3. Creating semantic chunks with metadata
4. Generating embeddings using Sentence Transformers
5. Indexing in Pinecone for fast retrieval

## 🚀 Quick Start

After installation, test the API:

```bash
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{"query": "What are the rules for funded trading challenges?"}'
```

Or visit `http://localhost:8000/docs` for interactive API documentation.

## 🆘 Support

For issues and questions:
1. Check the logs in the `logs/` directory
2. Verify your environment variables
3. Ensure all dependencies are installed
4. Check the API documentation at `/docs`