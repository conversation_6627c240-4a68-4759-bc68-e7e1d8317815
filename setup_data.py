"""Setup script to process and index the WhatsApp data."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.data_processor import WhatsAppDataProcessor
from app.services.vector_store import PineconeVectorStore
from config import settings


async def setup_data():
    """Process WhatsApp data and index it in Pinecone."""
    print("=" * 60)
    print("Trading Assistant - Data Setup")
    print("=" * 60)
    
    try:
        # Check if data file exists
        if not Path(settings.data_file_path).exists():
            print(f"Error: Data file not found at {settings.data_file_path}")
            print("Please ensure the WhatsApp chat file is in the correct location.")
            return False
        
        print(f"Processing data from: {settings.data_file_path}")
        
        # Initialize components
        print("\n1. Initializing data processor...")
        processor = WhatsAppDataProcessor()
        
        print("2. Initializing vector store...")
        vector_store = PineconeVectorStore()
        
        # Process data
        print("3. Processing WhatsApp chat data...")
        chunks = processor.process_data()
        
        if not chunks:
            print("Error: No document chunks were created from the data.")
            return False
        
        print(f"   Created {len(chunks)} document chunks")
        
        # Index data
        print("4. Indexing documents in Pinecone...")
        success = vector_store.upsert_documents(chunks)
        
        if success:
            print("✅ Data setup completed successfully!")
            
            # Show stats
            stats = vector_store.get_index_stats()
            print(f"\nIndex Statistics:")
            print(f"   Total vectors: {stats.get('total_vectors', 0)}")
            print(f"   Dimension: {stats.get('dimension', 0)}")
            print(f"   Index fullness: {stats.get('index_fullness', 0):.2%}")
            
            return True
        else:
            print("❌ Failed to index documents")
            return False
            
    except Exception as e:
        print(f"❌ Error during data setup: {e}")
        return False


def main():
    """Main function."""
    print("Starting data setup process...")
    
    # Check environment variables
    required_vars = ["GOOGLE_API_KEY", "PINECONE_API_KEY", "PINECONE_ENVIRONMENT"]
    missing_vars = []
    
    for var in required_vars:
        if not getattr(settings, var.lower(), None):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these variables in your .env file or environment.")
        return
    
    # Run setup
    success = asyncio.run(setup_data())
    
    if success:
        print("\n🎉 Setup complete! You can now start the Trading Assistant:")
        print("   python main.py")
    else:
        print("\n❌ Setup failed. Please check the errors above and try again.")


if __name__ == "__main__":
    main()
