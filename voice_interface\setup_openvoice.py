"""Setup script for OpenVoice TTS installation."""

import os
import sys
import subprocess
import urllib.request
from pathlib import Path

# Add the parent directory to the path so we can import voice_interface modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def check_git():
    """Check if git is installed."""
    try:
        result = subprocess.run(["git", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Git is available")
            return True
        else:
            print("❌ Git not found")
            return False
    except FileNotFoundError:
        print("❌ Git not installed")
        return False


def setup_openvoice():
    """Set up OpenVoice TTS."""
    print("🎭 OpenVoice TTS Setup")
    print("=" * 40)

    # Check prerequisites
    if not check_git():
        print("\n❌ Git is required for OpenVoice setup")
        print("Please install Git from: https://git-scm.com/downloads")
        return False

    # Setup OpenVoice manually without importing the class
    try:
        print("\n🚀 Starting OpenVoice setup...")

        # Define paths
        openvoice_dir = Path("voice_interface/OpenVoice")

        # Step 1: Clone OpenVoice repository
        if not openvoice_dir.exists() or not (openvoice_dir / "inference.py").exists():
            print("📥 Cloning OpenVoice repository...")
            clone_openvoice(openvoice_dir)
        else:
            print("✅ OpenVoice repository already exists")

        # Step 2: Install dependencies in current environment
        print("📦 Installing OpenVoice dependencies in current environment...")
        install_dependencies_current_env(openvoice_dir)

        # Step 3: Setup directories
        print("📁 Setting up directories...")
        setup_directories(openvoice_dir)

        print("\n✅ OpenVoice setup completed successfully!")

        # Show next steps
        print("\n📋 Next Steps:")
        print("1. Download OpenVoice model checkpoints:")
        print("   - Visit: https://github.com/myshell-ai/OpenVoice")
        print("   - Follow model download instructions")
        print(f"   - Place models in: {openvoice_dir}/checkpoints/")

        print("\n2. Provide reference audio:")
        print("   - Record or find a 3-10 second audio sample")
        print("   - Save as WAV format")
        print(f"   - Place at: {openvoice_dir}/reference_audio/reference.wav")

        print("\n3. Test OpenVoice:")
        print("   uv run python voice_interface/test_openvoice_complete.py")

        return True

    except Exception as e:
        print(f"\n❌ Setup error: {e}")
        import traceback
        traceback.print_exc()
        return False


def clone_openvoice(openvoice_dir):
    """Clone the OpenVoice repository."""
    try:
        # Create voice_interface directory if it doesn't exist
        os.makedirs("voice_interface", exist_ok=True)

        # Clone the repository
        cmd = [
            "git", "clone",
            "https://github.com/myshell-ai/OpenVoice.git",
            str(openvoice_dir)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            raise Exception(f"Git clone failed: {result.stderr}")

        print("✅ OpenVoice repository cloned successfully")

    except Exception as e:
        raise Exception(f"Failed to clone OpenVoice: {e}")


def install_dependencies_current_env(openvoice_dir):
    """Install OpenVoice dependencies in current environment using uv."""
    try:
        # Install requirements if they exist
        requirements_file = openvoice_dir / "requirements.txt"
        if requirements_file.exists():
            print("📋 Installing requirements.txt...")
            cmd = ["uv", "add", "--requirements", str(requirements_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                print(f"⚠️  Some requirements may not be available via uv: {result.stderr}")
                print("🔧 Trying individual package installation...")

                # Try to install common packages individually
                common_packages = [
                    "torch", "torchaudio", "numpy", "scipy",
                    "librosa", "soundfile", "pydub"
                ]

                for package in common_packages:
                    try:
                        print(f"📦 Installing {package}...")
                        cmd = ["uv", "add", package]
                        subprocess.run(cmd, check=True, capture_output=True, text=True)
                    except subprocess.CalledProcessError:
                        print(f"⚠️  Could not install {package} via uv")
        else:
            print("📦 Installing common OpenVoice dependencies...")
            # Install essential packages
            essential_packages = [
                "torch", "torchaudio", "numpy", "scipy",
                "librosa", "soundfile", "pydub", "matplotlib"
            ]

            for package in essential_packages:
                try:
                    print(f"📦 Installing {package}...")
                    cmd = ["uv", "add", package]
                    subprocess.run(cmd, check=True, capture_output=True, text=True)
                except subprocess.CalledProcessError as e:
                    print(f"⚠️  Could not install {package}: {e}")

        print("✅ Dependencies installation completed")

    except Exception as e:
        print(f"⚠️  Dependency installation had issues: {e}")
        print("💡 You may need to install some packages manually later")


def setup_directories(openvoice_dir):
    """Set up required directories."""
    try:
        # Create checkpoints directory
        checkpoints_dir = openvoice_dir / "checkpoints"
        checkpoints_dir.mkdir(exist_ok=True)

        # Create reference audio directory
        ref_audio_dir = openvoice_dir / "reference_audio"
        ref_audio_dir.mkdir(exist_ok=True)

        print("✅ Directories created")

    except Exception as e:
        raise Exception(f"Failed to setup directories: {e}")


def download_sample_reference_audio():
    """Download a sample reference audio file."""
    try:
        print("\n🎤 Setting up sample reference audio...")
        
        # Create reference audio directory
        ref_dir = Path("voice_interface/OpenVoice/reference_audio")
        ref_dir.mkdir(parents=True, exist_ok=True)
        
        # Note: In a real implementation, you would download a sample audio file
        # For now, we'll create instructions for the user
        
        ref_file = ref_dir / "reference.wav"
        
        print(f"📁 Reference audio should be placed at: {ref_file}")
        print("\n💡 Reference audio requirements:")
        print("   - Duration: 3-10 seconds")
        print("   - Format: WAV, 16-bit, 22050Hz or 44100Hz")
        print("   - Content: Clear speech, preferably the voice you want to clone")
        print("   - Quality: Good quality recording, minimal background noise")
        
        print("\n🎯 You can:")
        print("   1. Record your own voice saying a few sentences")
        print("   2. Use any existing audio file with the voice you want")
        print("   3. Convert MP3 to WAV using online converters")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up reference audio: {e}")
        return False


def main():
    """Main setup function."""
    print("Welcome to OpenVoice TTS Setup!")
    print("This will set up realistic voice synthesis for your trading assistant.")
    print()
    
    # Run setup
    success = setup_openvoice()
    
    if success:
        # Setup reference audio
        download_sample_reference_audio()
        
        print("\n🎉 Setup completed!")
        print("\n🚀 Quick Start:")
        print("1. Download models (see instructions above)")
        print("2. Add reference audio file")
        print("3. Run: uv run python voice_interface/run_voice_assistant.py")
        
    else:
        print("\n❌ Setup failed. Please check the errors above.")
        print("\n🔧 Troubleshooting:")
        print("- Make sure you have internet connection")
        print("- Ensure Git is installed and accessible")
        print("- Check that you have sufficient disk space")


if __name__ == "__main__":
    main()
