"""Setup script for OpenVoice TTS installation."""

import os
import sys
import subprocess
import urllib.request
from pathlib import Path


def check_git():
    """Check if git is installed."""
    try:
        result = subprocess.run(["git", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Git is available")
            return True
        else:
            print("❌ Git not found")
            return False
    except FileNotFoundError:
        print("❌ Git not installed")
        return False


def setup_openvoice():
    """Set up OpenVoice TTS."""
    print("🎭 OpenVoice TTS Setup")
    print("=" * 40)
    
    # Check prerequisites
    if not check_git():
        print("\n❌ Git is required for OpenVoice setup")
        print("Please install Git from: https://git-scm.com/downloads")
        return False
    
    # Import and run OpenVoice setup
    try:
        from voice_interface.openvoice_tts import OpenVoiceTTS
        
        print("\n🚀 Starting OpenVoice setup...")
        tts = OpenVoiceTTS()
        
        if tts.is_initialized:
            print("\n✅ OpenVoice setup completed successfully!")
            
            # Show next steps
            print("\n📋 Next Steps:")
            print("1. Download OpenVoice model checkpoints:")
            print("   - Visit: https://github.com/myshell-ai/OpenVoice")
            print("   - Follow model download instructions")
            print(f"   - Place models in: {tts.openvoice_dir}/checkpoints/")
            
            print("\n2. Provide reference audio:")
            print("   - Record or find a 3-10 second audio sample")
            print("   - Save as WAV format")
            print(f"   - Place at: {tts.reference_audio}")
            
            print("\n3. Test OpenVoice:")
            print("   uv run python voice_interface/openvoice_tts.py")
            
            return True
        else:
            print("\n❌ OpenVoice setup failed")
            return False
            
    except Exception as e:
        print(f"\n❌ Setup error: {e}")
        return False


def download_sample_reference_audio():
    """Download a sample reference audio file."""
    try:
        print("\n🎤 Setting up sample reference audio...")
        
        # Create reference audio directory
        ref_dir = Path("voice_interface/OpenVoice/reference_audio")
        ref_dir.mkdir(parents=True, exist_ok=True)
        
        # Note: In a real implementation, you would download a sample audio file
        # For now, we'll create instructions for the user
        
        ref_file = ref_dir / "reference.wav"
        
        print(f"📁 Reference audio should be placed at: {ref_file}")
        print("\n💡 Reference audio requirements:")
        print("   - Duration: 3-10 seconds")
        print("   - Format: WAV, 16-bit, 22050Hz or 44100Hz")
        print("   - Content: Clear speech, preferably the voice you want to clone")
        print("   - Quality: Good quality recording, minimal background noise")
        
        print("\n🎯 You can:")
        print("   1. Record your own voice saying a few sentences")
        print("   2. Use any existing audio file with the voice you want")
        print("   3. Convert MP3 to WAV using online converters")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up reference audio: {e}")
        return False


def main():
    """Main setup function."""
    print("Welcome to OpenVoice TTS Setup!")
    print("This will set up realistic voice synthesis for your trading assistant.")
    print()
    
    # Run setup
    success = setup_openvoice()
    
    if success:
        # Setup reference audio
        download_sample_reference_audio()
        
        print("\n🎉 Setup completed!")
        print("\n🚀 Quick Start:")
        print("1. Download models (see instructions above)")
        print("2. Add reference audio file")
        print("3. Run: uv run python voice_interface/run_voice_assistant.py")
        
    else:
        print("\n❌ Setup failed. Please check the errors above.")
        print("\n🔧 Troubleshooting:")
        print("- Make sure you have internet connection")
        print("- Ensure Git is installed and accessible")
        print("- Check that you have sufficient disk space")


if __name__ == "__main__":
    main()
