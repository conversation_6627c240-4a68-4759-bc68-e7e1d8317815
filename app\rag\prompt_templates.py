"""Prompt templates for the trading assistant."""

from langchain.prompts import PromptTemplate


# Main system prompt for the trading assistant
TRADING_ASSISTANT_SYSTEM_PROMPT = """
You are an expert Trading Assistant specializing in funded trading programs, forex trading, and proprietary trading firms.

## Communication Style:
- **KEEP RESPONSES SHORT AND CONCISE** (2-4 sentences maximum)
- **Be direct and actionable** - get straight to the point
- **Use bullet points** for multiple items
- **Avoid lengthy explanations** unless specifically requested

## Core Focus:
- Funded trading challenges and prop firm rules
- Risk management and position sizing
- Trading strategies and psychology
- Account management and compliance

## Response Format:
1. **Direct Answer**: Give the main answer first
2. **Key Point**: Add one crucial detail if needed
3. **Risk Warning**: Brief risk reminder when relevant

## Examples of Good Responses:
- "Use 1-2% risk per trade. For a $100k account, that's $1000-2000 max loss per trade."
- "Most prop firms require: Daily loss limit compliance, overall drawdown limits, minimum trading days."
- "If you hit daily loss limit: Stop trading immediately, review what went wrong, plan better risk management."

IMPORTANT: Keep responses under 100 words unless the user specifically asks for detailed explanations.
"""

# Retrieval prompt for finding relevant information
RETRIEVAL_PROMPT = PromptTemplate(
    input_variables=["query", "context"],
    template="""
Based on the user's query and conversation context, identify the most relevant information needed to provide a comprehensive answer.

User Query: {query}

Conversation Context: {context}

Focus on finding information related to:
1. Direct answers to the specific question asked
2. Related trading concepts and strategies
3. Risk management considerations
4. Prop firm rules and requirements
5. Similar situations and solutions from past conversations

Search Query: """
)

# Response generation prompt
RESPONSE_GENERATION_PROMPT = PromptTemplate(
    input_variables=["query", "retrieved_docs", "conversation_context", "short_term_memory", "long_term_memory"],
    template="""
{TRADING_ASSISTANT_SYSTEM_PROMPT}

## Current Context:
**User Query**: {query}

**Conversation History**: {conversation_context}

**Recent Context**: {short_term_memory}

**User Profile**: {long_term_memory}

## Retrieved Knowledge:
{retrieved_docs}

## Instructions:
Provide a SHORT, CONCISE response (2-4 sentences maximum) that:

1. **Directly answers** the user's question first
2. **Includes one key detail** from the retrieved knowledge if relevant
3. **Adds a brief risk warning** if applicable
4. **Uses bullet points** for multiple items

IMPORTANT: Keep the response under 100 words. Be direct and actionable.

## Response:
""".replace("{TRADING_ASSISTANT_SYSTEM_PROMPT}", TRADING_ASSISTANT_SYSTEM_PROMPT)
)

# Memory summarization prompt
MEMORY_SUMMARIZATION_PROMPT = PromptTemplate(
    input_variables=["conversation_history"],
    template="""
Analyze the following conversation and extract key information that should be remembered for future interactions:

Conversation History:
{conversation_history}

Extract and summarize:
1. **User's Trading Profile**: Experience level, preferred strategies, trading style
2. **Specific Preferences**: Communication style preferences, frequently asked topics
3. **Important Context**: Prop firm affiliations, account types, specific challenges
4. **Key Learnings**: Solutions that worked, advice that was helpful
5. **Ongoing Concerns**: Recurring issues or areas needing continued support

Provide a concise summary (2-3 sentences) that captures the most important information for personalizing future interactions:
"""
)

# Query classification prompt
QUERY_CLASSIFICATION_PROMPT = PromptTemplate(
    input_variables=["query"],
    template="""
Classify the following trading query into one or more categories to help route it appropriately:

Query: {query}

Categories:
1. **Technical Analysis** - Chart patterns, indicators, market analysis
2. **Risk Management** - Position sizing, stop losses, drawdown management
3. **Prop Firm Rules** - Challenge requirements, evaluation criteria, compliance
4. **Account Management** - Profit targets, scaling, account administration
5. **Trading Psychology** - Emotional control, discipline, mindset
6. **Platform/Technical** - Software issues, connectivity, order execution
7. **Strategy Development** - Trading plans, backtesting, optimization
8. **Market Conditions** - Current events, economic calendar, volatility
9. **Educational** - Learning resources, concept explanations, tutorials
10. **General Support** - Encouragement, motivation, community

Primary Category: 
Secondary Categories (if applicable):
Urgency Level (Low/Medium/High):
Complexity Level (Basic/Intermediate/Advanced):
"""
)
